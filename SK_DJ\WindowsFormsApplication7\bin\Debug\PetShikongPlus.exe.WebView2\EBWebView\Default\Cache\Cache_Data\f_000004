/**
 * 聊天WebSocket客户端
 * 用于替换桌面应用的window.external聊天功能
 */
class ChatWebSocketClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.playerId = null;
        this.playerName = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000; // 3秒重连延迟
        this.heartbeatInterval = null;
        this.heartbeatDelay = 30000; // 30秒心跳
    }

    /**
     * 连接WebSocket服务器
     */
    connect() {
        try {
            // 检查用户认证状态
            if (!this.validateUserAuth()) {
                console.error('用户认证失败，无法连接聊天服务');
                this.showAuthError();
                return false;
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/universal`;

            console.log('正在连接WebSocket:', wsUrl);
            console.log('用户信息:', { playerId: this.playerId, playerName: this.playerName });

            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = this.onOpen.bind(this);
            this.ws.onmessage = this.onMessage.bind(this);
            this.ws.onclose = this.onClose.bind(this);
            this.ws.onerror = this.onError.bind(this);

            return true;
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.scheduleReconnect();
            return false;
        }
    }

    /**
     * 验证用户认证状态
     */
    validateUserAuth() {
        // 1. 优先从认证管理器获取用户信息
        if (window.authManager && window.authManager.isLoggedIn()) {
            const user = window.authManager.getCurrentUser();
            if (user && user.userId) {
                this.playerId = user.userId;
                this.playerName = user.nickname || user.username || `用户${user.userId}`;
                console.log('✅ 从认证管理器获取用户信息:', user);
                return true;
            }
        }

        // 2. 备用方案：从全局函数获取
        if (typeof window.getCurrentUserId === 'function') {
            const userId = window.getCurrentUserId();
            if (userId && userId > 0) {
                this.playerId = userId;
                this.playerName = `用户${userId}`;
                console.log('✅ 从全局函数获取用户ID:', userId);
                return true;
            }
        }

        // 3. 兼容性：从页面元素获取
        const userIdFromPage = $('.用户ID').text() || $('.userId').text();
        const userNameFromPage = $('.用户名').text() || $('.userName').text();
        if (userIdFromPage && userIdFromPage !== '0') {
            const userId = parseInt(userIdFromPage);
            if (!isNaN(userId) && userId > 0) {
                this.playerId = userId;
                this.playerName = userNameFromPage || `用户${userId}`;
                console.log('✅ 从页面元素获取用户信息:', { userId, userName: this.playerName });
                return true;
            }
        }

        // 4. 检查是否为开发环境，如果不是则跳转到登录页面
        if (!this.isDevelopmentMode()) {
            console.error('❌ 用户未登录，跳转到登录页面');
            this.redirectToLogin();
            return false;
        }

        // 5. 开发环境：提示用户登录
        console.warn('⚠️ 开发环境：用户未登录，聊天功能受限');
        return false;
    }

    /**
     * 跳转到登录页面
     */
    redirectToLogin() {
        const loginUrl = this.getLoginUrl();
        console.log('跳转到登录页面:', loginUrl);

        // 显示提示信息
        this.showAuthError('用户未登录，即将跳转到登录页面...');

        // 延迟跳转，让用户看到提示信息
        setTimeout(() => {
            window.location.href = loginUrl;
        }, 2000);
    }

    /**
     * 获取登录页面URL
     */
    getLoginUrl() {
        // 1. 检查是否有配置的登录URL
        if (window.LOGIN_URL) {
            return window.LOGIN_URL;
        }

        // 2. 根据当前路径推断登录页面
        const currentPath = window.location.pathname;
        if (currentPath.includes('/game/pages/')) {
            return '/game/pages/Login.html';
        }

        // 3. 默认登录页面
        return '/Login.html';
    }

    /**
     * 检查是否为开发环境
     */
    isDevelopmentMode() {
        return window.location.hostname === 'localhost' ||
               window.location.hostname === '127.0.0.1' ||
               window.location.hostname.includes('dev');
    }

    /**
     * 显示认证错误
     */
    showAuthError() {
        const errorMsg = '用户未登录或认证失败，无法使用聊天功能';
        if (typeof showChatError === 'function') {
            showChatError(errorMsg);
        } else if (window.loadingManager) {
            window.loadingManager.showError(errorMsg);
        } else {
            // 直接在聊天框显示错误
            if ($('#chatDiv').length > 0) {
                $('#chatDiv').append(`<div class="send_ms0"><font color="#dc3545">[错误]：${errorMsg}</font></div>`);
            } else {
                alert(errorMsg);
            }
        }
    }

    /**
     * WebSocket连接打开事件
     */
    onOpen(event) {
        console.log('WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 启动心跳
        this.startHeartbeat();
        
        // 显示连接成功消息
        this.displaySystemMessage('聊天服务连接成功', 'success');
        
        // 发送用户认证信息
        this.sendUserAuth();

        // 如果有用户信息，自动登录
        if (this.playerId && this.playerName) {
            this.login(this.playerId, this.playerName, '');
        }
    }

    /**
     * 发送用户认证信息到服务器
     */
    sendUserAuth() {
        if (!this.playerId || !this.playerName) {
            console.warn('用户信息不完整，跳过认证信息发送');
            return;
        }

        const authMessage = {
            type: 'user_auth',
            data: {
                userId: this.playerId,
                userName: this.playerName,
                timestamp: Date.now(),
                clientInfo: {
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    language: navigator.language,
                    url: window.location.href
                }
            }
        };

        try {
            this.ws.send(JSON.stringify(authMessage));
            console.log('✅ 已发送用户认证信息:', authMessage.data);
        } catch (error) {
            console.error('发送用户认证信息失败:', error);
        }
    }

    /**
     * WebSocket消息接收事件
     */
    onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        } catch (error) {
            console.error('解析WebSocket消息失败:', error, event.data);
        }
    }

    /**
     * WebSocket连接关闭事件
     */
    onClose(event) {
        console.log('WebSocket连接已关闭', event.code, event.reason);
        this.isConnected = false;
        this.stopHeartbeat();
        
        // 显示断开连接消息
        this.displaySystemMessage('聊天服务连接断开', 'warning');
        
        // 尝试重连
        if (event.code !== 1000) { // 非正常关闭
            this.scheduleReconnect();
        }
    }

    /**
     * WebSocket错误事件
     */
    onError(error) {
        console.error('WebSocket错误:', error);
        this.displaySystemMessage('聊天服务连接错误', 'error');
    }

    /**
     * 处理接收到的消息
     */
    handleMessage(message) {
        console.log('收到WebSocket消息:', message);

        // 处理认证响应
        if (message.type === 'auth_response') {
            this.handleAuthResponse(message);
            return;
        }

        switch (message.Type) {
            case 'chat':
                this.displayChatMessage(message);
                break;
            case 'private':
                this.displayPrivateMessage(message);
                break;
            case 'La': // 登录成功
                this.handleLoginSuccess(message);
                break;
            case 'system_notice':
                this.displaySystemNotice(message);
                break;
            case 'game_event':
                this.displayGameEvent(message);
                break;
            default:
                console.log('未处理的消息类型:', message.Type);
        }
    }

    /**
     * 发送聊天消息
     */
    sendChatMessage(content, type = 'chat', targetUserId = null) {
        if (!this.isConnected) {
            this.displaySystemMessage('聊天服务未连接，无法发送消息', 'error');
            return false;
        }

        if (!content || !content.trim()) {
            return false;
        }

        const message = {
            Type: type,
            playerId: this.playerId || 0,
            number: this.playerName || '游客',
            Name: this.playerName || '游客',
            Content: content,
            TargetUserId: targetUserId
        };
        
        try {
            this.ws.send(JSON.stringify(message));
            console.log('发送聊天消息:', message);
            return true;
        } catch (error) {
            console.error('发送消息失败:', error);
            this.displaySystemMessage('发送消息失败', 'error');
            return false;
        }
    }

    /**
     * 用户登录聊天服务
     */
    login(playerId, playerName, password = '') {
        this.playerId = playerId;
        this.playerName = playerName;
        
        if (!this.isConnected) {
            console.log('WebSocket未连接，等待连接后自动登录');
            return;
        }

        const loginMessage = {
            Type: "L",
            playerId: playerId,
            number: playerName,
            password: password,
            Name: playerName,
            Content: "login"
        };
        
        try {
            this.ws.send(JSON.stringify(loginMessage));
            console.log('发送登录消息:', loginMessage);
        } catch (error) {
            console.error('发送登录消息失败:', error);
        }
    }

    /**
     * 处理登录成功响应
     */
    handleLoginSuccess(message) {
        console.log('聊天登录成功:', message);
        this.playerId = message.playerId;
        this.playerName = message.Name;
        
        this.displaySystemMessage(`欢迎 ${message.Name} 进入聊天室`, 'success');
    }

    /**
     * 显示聊天消息
     */
    displayChatMessage(message) {
        const colortext = message.Content.split("&&");
        let color = "#000";
        let content = colortext[0];
        
        // 解析颜色信息
        if (colortext.length >= 2) {
            const colorName = colortext[1];
            switch(colorName) {
                case "黑": color = "#000"; break;
                case "绿": color = "#33cc00"; break;
                case "粉": color = "#ff3399"; break;
                case "蓝": color = "#0000ff"; break;
                default: color = "#000";
            }
        }
        
        const messageHtml = `<div class='send_ms0'>
            <a href="javascript:void(0);">
                <font onclick="sl('${message.number}')" color="#ff0000">${message.number}</font>
            </a>说：
            <font color="${color}">${content}</font>
        </div>`;
        
        this.appendToChatDiv(messageHtml);
    }

    /**
     * 显示私聊消息
     */
    displayPrivateMessage(message) {
        const colortext = message.Content.split("&&");
        let color = "#28a745"; // 私聊使用绿色
        let content = colortext[0];
        
        const messageHtml = `<div class='send_ms0' style='background: rgba(40, 167, 69, 0.1);'>
            <font color="#28a745">[私聊]</font>
            <a href="javascript:void(0);">
                <font onclick="sl('${message.number}')" color="#ff0000">${message.number}</font>
            </a>：
            <font color="${color}">${content}</font>
        </div>`;
        
        this.appendToChatDiv(messageHtml);
    }

    /**
     * 显示系统消息
     */
    displaySystemMessage(content, type = 'info') {
        let color = '#0041F9';
        switch(type) {
            case 'success': color = '#28a745'; break;
            case 'warning': color = '#ffc107'; break;
            case 'error': color = '#dc3545'; break;
            default: color = '#0041F9';
        }
        
        const messageHtml = `<div class='send_ms0'>
            <font color="${color}">[系统]：${content}</font>
        </div>`;
        
        this.appendToChatDiv(messageHtml);
    }

    /**
     * 显示系统公告
     */
    displaySystemNotice(message) {
        const messageHtml = `<div class='send_ms0' style='background: rgba(255, 193, 7, 0.1);'>
            <font color="#ffc107">[公告]：${message.Content}</font>
        </div>`;
        
        this.appendToChatDiv(messageHtml);
    }

    /**
     * 显示游戏事件
     */
    displayGameEvent(message) {
        const messageHtml = `<div class='send_ms0' style='background: rgba(0, 123, 255, 0.1);'>
            <font color="#007bff">[事件]：${message.Description}</font>
        </div>`;
        
        this.appendToChatDiv(messageHtml);
    }

    /**
     * 添加消息到聊天框
     */
    appendToChatDiv(messageHtml) {
        const chatDiv = document.getElementById('chatDiv');
        if (chatDiv) {
            chatDiv.innerHTML += messageHtml;
            chatDiv.scrollTop = chatDiv.scrollHeight;
        }
    }

    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
                // 发送心跳消息
                try {
                    this.ws.send(JSON.stringify({ Type: 'heartbeat' }));
                } catch (error) {
                    console.error('发送心跳失败:', error);
                }
            }
        }, this.heartbeatDelay);
    }

    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`${this.reconnectDelay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectDelay);
            
            // 递增重连延迟
            this.reconnectDelay = Math.min(this.reconnectDelay * 1.5, 30000);
        } else {
            console.error('达到最大重连次数，停止重连');
            this.displaySystemMessage('聊天服务连接失败，请刷新页面重试', 'error');
        }
    }

    /**
     * 断开连接
     */
    disconnect() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close(1000, 'User disconnect');
        }
        this.isConnected = false;
    }

    /**
     * 处理认证响应
     */
    handleAuthResponse(message) {
        if (message.success) {
            console.log('✅ 用户认证成功:', message.data);
            this.displaySystemMessage(`用户认证成功: ${message.data.userName}`, 'success');

            // 更新用户信息
            if (message.data) {
                this.playerId = message.data.userId;
                this.playerName = message.data.userName;
            }
        } else {
            console.error('❌ 用户认证失败:', message.message);
            this.displaySystemMessage(`用户认证失败: ${message.message}`, 'error');

            // 认证失败时可以选择断开连接或重试
            this.showAuthError();
        }
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            playerId: this.playerId,
            playerName: this.playerName,
            reconnectAttempts: this.reconnectAttempts,
            isAuthenticated: this.playerId && this.playerName
        };
    }
}

// 全局聊天客户端实例
window.chatClient = null;
