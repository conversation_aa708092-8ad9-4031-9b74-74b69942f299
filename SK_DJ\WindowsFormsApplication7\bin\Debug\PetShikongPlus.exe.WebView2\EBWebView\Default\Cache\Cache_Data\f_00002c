/**
 * PetInfo页面API适配器
 * 基于现有服务的宠物信息页面API对接
 */

// API基础配置
const API_BASE_URL = '/api';

// 获取当前用户ID的辅助函数
function getCurrentUserId() {
    // 优先从认证管理器获取
    if (window.authManager && window.authManager.isLoggedIn()) {
        const userId = window.authManager.getCurrentUserId();
        if (userId && userId !== 1) {
            return userId;
        }
    }

    // 从localStorage获取
    const storedUserId = parseInt(localStorage.getItem('userId') || '0');
    if (storedUserId && storedUserId !== 1) {
        return storedUserId;
    }

    // 从sessionStorage获取
    const sessionUserId = parseInt(sessionStorage.getItem('userId') || '0');
    if (sessionUserId && sessionUserId !== 1) {
        return sessionUserId;
    }

    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const userIdFromUrl = urlParams.get('userId');
    if (userIdFromUrl) {
        const userId = parseInt(userIdFromUrl);
        if (userId && userId !== 1) {
            return userId;
        }
    }

    // 检查是否为开发环境
    const isDev = window.location.hostname === 'localhost' ||
                 window.location.hostname === '127.0.0.1' ||
                 window.location.hostname.includes('dev');

    if (isDev) {
        console.warn('⚠️ 开发环境：未找到有效的用户ID，使用默认值1');
        return 1;
    } else {
        console.error('❌ 生产环境：用户未登录，无法获取用户ID');
        // 生产环境下跳转到登录页面
        if (typeof redirectToLogin === 'function') {
            redirectToLogin();
        } else {
            window.location.href = '/Login.html';
        }
        return null;
    }
}

// 扩展window.external对象，添加新的API方法
if (typeof window.external === 'undefined') {
    window.external = {};
}

// 数据转换函数：将API格式转换为页面需要的中文字段格式
function convertPetDataToChineseFormat(pet, petSkills = []) {
    if (!pet) return null;

    const skillDisplay = petSkills.length > 0
        ? petSkills.map(skill => (skill.skillConfig && skill.skillConfig.skillName) || '未知技能').join(', ')
        : '';

    return {
        宠物序号: pet.id,
        宠物名字: pet.name || '未知宠物',
        等级: pet.level || 1,
        五行: pet.element || '无',
        境界: pet.realm || '无',
        生命: pet.hp || 0,
        魔法: pet.mp || 0,
        最大生命: pet.hp || 0,
        最大魔法: pet.mp || 0,
        成长: pet.growth || 1,
        形象: pet.petNo || pet.pet_no || 264,
        状态: pet.isMain ? "0" : "1",
        已进化次数: 0,
        加深: 0,
        抵消: 0,
        吸血: 0,
        技能显示: skillDisplay,
        技能列表: petSkills,
        指定形象: null
    };
}

// 宠物信息页面API方法
Object.assign(window.external, {

    // 切换宠物 - 移除加载提示，使用静默切换
    async switchPet(petId) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Game/switch-pet`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId, petId: parseInt(petId) })
            });

            const result = await response.json();
            if (result.success) {
                // 更新主宠物信息 - 转换数据格式
                if (result.data.petInfo) {
                    // 获取宠物技能信息
                    const petSkills = result.data.petSkills || [];
                    // 转换为中文字段格式
                    const convertedPetData = convertPetDataToChineseFormat(result.data.petInfo, petSkills);
                    console.log('切换宠物 - 转换后的数据:', convertedPetData);
                    updateMainPet(convertedPetData);
                }

                // 通过独立接口更新装备信息
                if (result.data.petInfo && typeof window.loadPetEquipments === 'function') {
                    const petNo = result.data.petInfo.id || result.data.petInfo.id;
                    const userId = getCurrentUserId();
                    console.log('切换宠物，通过独立接口加载装备数据 - 宠物编号:', petNo, '用户ID:', userId);
                    window.loadPetEquipments(petNo, userId);
                } else if (typeof window.loadZB === 'function') {
                    // 降级处理：清空装备显示
                    console.log('无宠物数据，清空装备显示');
                    window.loadZB([]);
                }

                // 静默成功，不显示提示框
                console.log('切换宠物成功');
                return true;
            } else {
                // 只在失败时显示错误提示
                console.error('切换宠物失败:', result.message);
                return false;
            }
        } catch (error) {
            console.error('切换宠物时发生错误:', error);
            return false;
        }
    },

    async _fallbackSwitchPet(petId) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Game/switch-pet`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId, petId: parseInt(petId) })
            });

            const result = await response.json();
            if (result.success) {
                if (result.data.petInfo) {
                    // 获取宠物技能信息
                    const petSkills = result.data.petSkills || [];
                    // 转换为中文字段格式
                    const convertedPetData = convertPetDataToChineseFormat(result.data.petInfo, petSkills);
                    console.log('切换宠物(兼容版本) - 转换后的数据:', convertedPetData);
                    updateMainPet(convertedPetData);
                }
                if (result.data.equipment) {
                    loadZB(JSON.stringify(result.data.equipment));
                }
                return true;
            }
            return false;
        } catch (error) {
            console.error('切换宠物失败:', error);
            return false;
        }
    },

    // 获取宠物技能列表（使用已存在的接口）
    async getPetSkills(petId) {
        const cacheKey = window.cacheManager?.generateKey('pet-skills', { petId });

        if (window.cacheManager) {
            return await window.cacheManager.wrapApiCall(cacheKey, async () => {
                const response = await fetch(`${API_BASE_URL}/Skill/pet/${petId}/skills`);
                const result = await response.json();
                return Array.isArray(result) ? result : [];
            }, window.cacheManager.ttlConfig['pet-skills']);
        }

        try {
            const response = await fetch(`${API_BASE_URL}/Skill/pet/${petId}/skills`);
            const result = await response.json();
            return Array.isArray(result) ? result : [];
        } catch (error) {
            console.error('获取宠物技能失败:', error);
            return [];
        }
    },

    // 技能升级（使用已存在的接口）
    async upgradeSkill(petId, skillId) {
        try {
            const response = await fetch(`${API_BASE_URL}/Skill/pet/${petId}/upgrade`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ skillId: skillId })
            });
            const result = await response.json();

            // 操作成功后清除相关缓存
            if (result.success && window.cacheManager) {
                window.cacheManager.invalidatePetCache(petId);
            }

            return result;
        } catch (error) {
            console.error('技能升级失败:', error);
            return { success: false, message: '网络错误' };
        }
    },

    // 遗忘技能（使用已存在的接口）
    async forgetSkill(petId, skillId) {
        try {
            const response = await fetch(`${API_BASE_URL}/Skill/pet/${petId}/forget`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ skillId: skillId })
            });
            const result = await response.json();
            const success = result === true;

            // 操作成功后清除相关缓存
            if (success && window.cacheManager) {
                window.cacheManager.invalidatePetCache(petId);
            }

            return { success: success, message: success ? '遗忘成功' : '遗忘失败' };
        } catch (error) {
            console.error('遗忘技能失败:', error);
            return { success: false, message: '网络错误' };
        }
    },

    // 学习技能（使用已存在的接口）
    async learnSkill(petId, skillId) {
        try {
            const response = await fetch(`${API_BASE_URL}/Skill/pet/${petId}/learn`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ skillId: skillId })
            });
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('学习技能失败:', error);
            return { success: false, message: '网络错误' };
        }
    },

    // 获取宠物主动技能（使用已存在的接口）
    async getPetActiveSkill(petId) {
        try {
            const response = await fetch(`${API_BASE_URL}/Skill/pet/${petId}/active-skill`);
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('获取宠物主动技能失败:', error);
            return null;
        }
    },

    // 获取宠物被动技能（使用已存在的接口）
    async getPetPassiveSkills(petId) {
        try {
            const response = await fetch(`${API_BASE_URL}/Skill/pet/${petId}/passive-skills`);
            const result = await response.json();
            return Array.isArray(result) ? result : [];
        } catch (error) {
            console.error('获取宠物被动技能失败:', error);
            return [];
        }
    },

    // 卸下装备（使用现有接口）
    async unequipItem(userEquipmentId) {
        try {
            const userId = getCurrentUserId();
            const response = await fetch(`${API_BASE_URL}/Equipment/unequip`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: userId,
                    userEquipmentId: parseInt(userEquipmentId)
                })
            });
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('卸下装备失败:', error);
            return { success: false, message: '网络错误' };
        }
    },

    // 获取宠物信息页面数据（新增接口）
    async getPetInfoPageData(userId, options = {}) {
        const cacheKey = window.cacheManager?.generateKey('pet-info', { userId });
        const showLoading = options.showLoading !== false; // 默认显示loading，可通过传入false关闭

        if (window.cacheManager) {
            return await window.cacheManager.wrapApiCall(cacheKey, async () => {
                if (showLoading && window.loadingManager) {
                    return await window.loadingManager.wrapApiCall(async () => {
                        const response = await fetch(`${API_BASE_URL}/Game/pet-info/${userId}`);
                        const result = await response.json();
                        return result;
                    }, '正在获取宠物信息...');
                } else {
                    const response = await fetch(`${API_BASE_URL}/Game/pet-info/${userId}`);
                    const result = await response.json();
                    return result;
                }
            }, window.cacheManager.ttlConfig['pet-info']);
        }

        // 无缓存管理器时，根据showLoading决定是否显示loading
        if (showLoading && window.loadingManager) {
            return await window.loadingManager.wrapApiCall(async () => {
                const response = await fetch(`${API_BASE_URL}/Game/pet-info/${userId}`);
                return await response.json();
            }, '正在获取宠物信息...');
        }

        return this._fallbackGetPetInfoPageData(userId);
    },

    async _fallbackGetPetInfoPageData(userId) {
        try {
            const response = await fetch(`${API_BASE_URL}/Game/pet-info/${userId}`);
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('获取宠物信息页面数据失败:', error);
            return { success: false, message: '网络错误' };
        }
    },

    // 装备相关API方法
    async unequipItem(userEquipmentId) {
        try {
            const userId = getCurrentUserId();
            console.log('卸下装备 - 装备ID:', userEquipmentId, '用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Equipment/unequip`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: userId,
                    userEquipmentId: parseInt(userEquipmentId)
                })
            });

            const result = await response.json();
            console.log('卸下装备API响应:', result);

            // 如果操作成功，自动刷新装备数据
            if (result.success) {
                await this.refreshCurrentPetEquipments();
            }

            return {
                Success: result.success || false,
                Message: result.message || '操作失败'
            };
        } catch (error) {
            console.error('卸下装备失败:', error);
            return {
                Success: false,
                Message: '网络错误，请重试'
            };
        }
    },

    async getEquipmentInfo(userEquipmentId) {
        try {
            console.log('获取装备详情 - 装备ID:', userEquipmentId);

            const response = await fetch(`${API_BASE_URL}/Equipment/${userEquipmentId}`);
            const result = await response.json();

            if (result.success && result.data) {
                return result.data;
            } else {
                console.error('获取装备详情失败:', result.message);
                return null;
            }
        } catch (error) {
            console.error('获取装备详情失败:', error);
            return null;
        }
    },

    async equipToPet(userEquipmentId, petId) {
        try {
            const userId = getCurrentUserId();
            console.log('装备到宠物 - 装备ID:', userEquipmentId, '宠物ID:', petId, '用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Equipment/equip`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: userId,
                    userEquipmentId: parseInt(userEquipmentId),
                    petId: parseInt(petId)
                })
            });

            const result = await response.json();
            console.log('装备到宠物API响应:', result);

            // 如果操作成功，自动刷新装备数据
            if (result.success) {
                await this.refreshCurrentPetEquipments();
            }

            return {
                Success: result.success || false,
                Message: result.message || '操作失败'
            };
        } catch (error) {
            console.error('装备到宠物失败:', error);
            return {
                Success: false,
                Message: '网络错误，请重试'
            };
        }
    },

    // 获取宠物装备
    async getPetEquipments(petNo, userId) {
        try {
            console.log('获取宠物装备 - 宠物编号:', petNo, '用户ID:', userId);

            const response = await fetch(`${API_BASE_URL}/Equipment/pet/${petNo}/user/${userId}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();
            console.log('宠物装备API响应:', result);

            if (result.success && result.data) {
                return result.data;
            } else {
                console.error('获取宠物装备失败:', result.message);
                return [];
            }
        } catch (error) {
            console.error('获取宠物装备失败:', error);
            return [];
        }
    },

    // 刷新当前宠物装备数据 - 用于装备穿戴/卸下后的实时更新
    async refreshCurrentPetEquipments() {
        try {
            const userId = getCurrentUserId();

            // 获取当前主宠物信息
            if (petInfoData && petInfoData.data && petInfoData.data.mainPet) {
                const petNo = petInfoData.data.mainPet.id || petInfoData.data.mainPet.id;
                console.log('刷新当前宠物装备 - 宠物编号:', petNo, '用户ID:', userId);

                if (typeof window.loadPetEquipments === 'function') {
                    await window.loadPetEquipments(petNo, userId);
                    console.log('装备数据刷新完成');
                }
            } else {
                console.warn('无法刷新装备：缺少主宠物信息');
            }
        } catch (error) {
            console.error('刷新装备数据失败:', error);
        }
    },

    // 获取单个宠物信息 - 优化版本：直接从本地数据获取，避免不必要的API调用
    getPetInfo(petId) {
        try {
            // 从页面的全局变量中获取宠物数据
            if (typeof allPetsData !== 'undefined' && allPetsData.length > 0) {
                const selectedPet = allPetsData.find(pet => pet.宠物序号 == petId);
                if (selectedPet) {
                    console.log('从本地数据获取宠物信息:', selectedPet);
                    return JSON.stringify(selectedPet);
                } else {
                    console.error('在本地数据中未找到宠物ID:', petId);
                }
            } else {
                console.warn('本地宠物数据未加载，请先调用 updatePetInfo_page()');
            }

            // 如果本地数据不可用，返回空对象
            return JSON.stringify({});
        } catch (error) {
            console.error('获取宠物信息失败:', error);
            return JSON.stringify({});
        }
    },

    // 页面数据更新方法
    async updatePetInfo_page() {
        try {
            const userId = getCurrentUserId();
            if (!userId) {
                console.error('无法获取用户ID');
                return;
            }

            // 重新获取宠物信息页面数据（关闭加载动画）
            const petInfoData = await this.getPetInfoPageData(userId, { showLoading: false });

            if (petInfoData.success && petInfoData.data) {
                console.log('开始更新页面数据...');

                // 转换并更新用户信息
                if (petInfoData.data.player && typeof window.showUserInfo === 'function') {
                    const userInfo = {
                        名字: petInfoData.data.player.nickname || petInfoData.data.player.account,
                        金币: petInfoData.data.player.gold || 0,
                        水晶: petInfoData.data.player.diamond || 0,
                        元宝: petInfoData.data.player.diamond || 0,
                        主宠名字: petInfoData.data.mainPet ? petInfoData.data.mainPet.name : '',
                        宠物数量: petInfoData.data.pets ? petInfoData.data.pets.length : 0,
                        sex: petInfoData.data.player.sex || '未知'
                    };
                    window.showUserInfo(JSON.stringify(userInfo));
                }

                // 保存宠物技能数据到全局变量
                if (petInfoData.data.petSkills && typeof window.allPetSkills !== 'undefined') {
                    window.allPetSkills = petInfoData.data.petSkills;
                    console.log('API适配器保存宠物技能数据:', window.allPetSkills);
                }

                // 转换并更新宠物列表
                if (petInfoData.data.pets && petInfoData.data.pets.length > 0 && typeof window.readPet === 'function') {
                    const petList = petInfoData.data.pets.map(pet => {
                        // 获取该宠物的技能信息
                        const petSkills = petInfoData.data.petSkills?.[pet.id] || [];
                        // 使用统一的转换函数
                        return convertPetDataToChineseFormat(pet, petSkills);
                    });
                    console.log('更新宠物信息页面 - 转换后的宠物列表:', petList);
                    window.readPet(JSON.stringify(petList));
                }

                // 装备信息通过独立接口获取，提高性能
                if (typeof window.loadPetEquipments === 'function' && petInfoData.data.mainPet) {
                    const petNo = petInfoData.data.mainPet.id || petInfoData.data.mainPet.id;
                    const userId = getCurrentUserId();
                    console.log('通过独立接口加载装备数据 - 宠物编号:', petNo, '用户ID:', userId);
                    window.loadPetEquipments(petNo, userId);
                } else if (typeof window.loadZB === 'function') {
                    // 降级处理：清空装备显示
                    console.log('无主宠物数据，清空装备显示');
                    window.loadZB([]);
                }

                console.log('页面数据更新完成');
            } else {
                console.error('获取宠物信息失败:', petInfoData?.message || '未知错误');
            }
        } catch (error) {
            console.error('更新宠物信息页面失败:', error);
        }
    }
});

// 创建简化的gameAPI对象
if (typeof window.gameAPI === 'undefined') {
    window.gameAPI = {};
}

// 技能相关方法（基于已存在的接口）
Object.assign(window.gameAPI, {
    updatePetInfo_page: () => window.external.updatePetInfo_page(),
    switchPet: (petId) => window.external.switchPet(petId),
    getPetSkills: (petId) => window.external.getPetSkills(petId),
    getPetActiveSkill: (petId) => window.external.getPetActiveSkill(petId),
    getPetPassiveSkills: (petId) => window.external.getPetPassiveSkills(petId),
    upgradeSkill: (petId, skillId) => window.external.upgradeSkill(petId, skillId),
    forgetSkill: (petId, skillId) => window.external.forgetSkill(petId, skillId),
    learnSkill: (petId, skillId) => window.external.learnSkill(petId, skillId),
    unequipItem: (equipmentId) => window.external.unequipItem(equipmentId),
    // 默认关闭加载动画，避免在页面初始化时出现遮罩
    getPetInfoPageData: (userId) => window.external.getPetInfoPageData(userId, { showLoading: false })
});

console.log('PetInfo API适配器已加载');
