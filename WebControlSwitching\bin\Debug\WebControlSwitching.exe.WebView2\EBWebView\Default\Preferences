{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 140}, "autofill": {"last_version_deduped": 140}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"available_dark_theme_options": "All", "recent_theme_color_list": [**********.0, **********.0, **********.0, **********.0, **********.0], "user_level_features_context": {}}, "browser_content_container_height": 283, "browser_content_container_width": 533, "browser_content_container_x": 0, "browser_content_container_y": 0, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "credentials_enable_service": false, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "edge": {"bookmarks": {"last_dup_info_record_time": "13402074797356841"}, "msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_sso_info": {"is_msa_first_profile": true, "msa_sso_algo_state": 1}, "services": {"signin_scoped_device_id": "f1a47ee3-3cf6-4a0d-bdee-f9093ce39fd5"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "hva_webui_action_status_dict": {}, "refresh_status_muted_until": "13402679587334849"}, "edge_ux_config": {"assignmentcontext": "", "dataversion": "0", "experimentvariables": {}, "flights": {}}, "edge_vpn": {"available": true}, "edge_wallet": {"passwords": {"password_lost_report_date": "13402074817396112"}}, "enterprise_profile_guid": "4cb532f0-40b3-42ba-83e7-15b4294dc9c0", "extension": {"installed_extension_count": 2}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "140.0.3485.66", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "fsd": {"retention_policy_last_version": 140}, "gaia_cookie": {"periodic_report_time_2": "*****************"}, "intl": {"selected_languages": "zh-CN,en,en-GB,en-US"}, "media": {"engagement": {"schema_version": 5}}, "muid": {"last_sync": "*****************", "values_seen": []}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}}, "password_manager": {"account_store_backup_password_cleaning_last_timestamp": "*****************", "account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_backup_password_cleaning_last_timestamp": "*****************", "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": true, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"file:///*,*": {"last_modified": "13402158069343731", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "geolocation_with_options": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "140.0.3485.54", "creation_time": "13402074787315506", "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "d6c12693-6595-4044-a7ed-dbd7428e8d5a", "exit_type": "Normal", "has_seen_signin_fre": false, "is_relative_to_aad": false, "last_time_obsolete_http_credentials_removed": 1757601247.389048, "last_time_password_store_metrics_reported": 1757601217.395834, "managed_user_id": "", "name": "用户配置 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_140.0.3485.54": 44.0, "feedback_rating_in_product_help_observed_session_time_key_140.0.3485.66": 9.0}, "password_hash_data_list": [], "signin_fre_seen_time": "13402074787328432", "were_old_google_logins_removed": true}, "reset_prepopulated_engines": false, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "sessions": {"event_log": [{"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402074920116900", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402075060833812", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402075080083384", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402075569566077", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402075995395516", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402076002244352", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402076016773330", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402076278645368", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402076291532324", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402157489414282", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402157544350836", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402157790021487", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402157795202820", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402157807674060", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13402157830779644", "type": 2, "window_count": 0}, {"crashed": false, "time": "13402158046511131", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}], "session_data_status": 3}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["zh-CN"]}, "syncing_theme_prefs_migrated_to_non_syncing": true, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}}