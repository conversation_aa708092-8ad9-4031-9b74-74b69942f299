[{"ContainingType": "WebApplication_HM.Controllers.DbFirstController", "Method": "CheckTableColumns", "RelativePath": "api/DbFirst/check-table/{tableName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.DbFirstController", "Method": "CompareStructure", "RelativePath": "api/DbFirst/compare-structure/{entityName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.DbFirstController", "Method": "GetEntities", "RelativePath": "api/DbFirst/entities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.DbFirstController", "Method": "GenerateEntity", "RelativePath": "api/DbFirst/generate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.DbFirstController", "Method": "GenerateDatabase", "RelativePath": "api/DbFirst/generate-database", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.DbFirstController", "Method": "SyncEntity", "RelativePath": "api/DbFirst/sync-entity/{entityName}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.DbFirstController", "Method": "SyncUserEntity", "RelativePath": "api/DbFirst/sync-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetEquipmentById", "RelativePath": "api/Equipment/{userEquipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.UserEquipmentDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "DeleteEquipment", "RelativePath": "api/Equipment/{userEquipmentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "CreateEquipment", "RelativePath": "api/Equipment/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.EquipmentCreateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.EquipmentObtainResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetElementInfo", "RelativePath": "api/Equipment/element/info/{userEquipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.ElementInfoResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetElementRelationships", "RelativePath": "api/Equipment/element/relationships", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Controllers.ElementRelationshipsResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "TransformElement", "RelativePath": "api/Equipment/element/transform", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.ElementTransformRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.ElementTransformResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "EnhanceEquipment", "RelativePath": "api/Equipment/enhance", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.EnhanceEquipmentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "EquipToPet", "RelativePath": "api/Equipment/equip", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.EquipToPetRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "EquipToPetLegacy", "RelativePath": "api/Equipment/equip-legacy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.EquipRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "SetGemstone", "RelativePath": "api/Equipment/gemstone", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.GemstoneOperationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GemstoneOperation", "RelativePath": "api/Equipment/gemstone-operation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.GemstoneOperationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "RemoveGemstone", "RelativePath": "api/Equipment/gemstone/{userEquipmentId}/{position}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "position", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetGemstoneConfigs", "RelativePath": "api/Equipment/gemstone/configs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.GemstoneConfigDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "EmbedGemstone", "RelativePath": "api/Equipment/gemstone/embed", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.EmbedGemstoneRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "RemoveGemstone", "RelativePath": "api/Equipment/gemstone/remove", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RemoveGemstoneRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetEquipmentGemstones", "RelativePath": "api/Equipment/gemstones/{userEquipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.GemstoneDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GrantEquipment", "RelativePath": "api/Equipment/grant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.EquipmentGrantRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.EquipmentObtainResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "ProcessObtainScript", "RelativePath": "api/Equipment/obtain-script", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.EquipmentObtainScriptRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.EquipmentObtainResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetPetEquipments", "RelativePath": "api/Equipment/pet/{petId}/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.UserEquipmentDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "ResolveEquipment", "RelativePath": "api/Equipment/resolve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.ResolveEquipmentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.ResolveResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "StrengthenEquipment", "RelativePath": "api/Equipment/strengthen", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.StrengthenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.StrengthenResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetStrengthenCost", "RelativePath": "api/Equipment/strengthen/cost/{userEquipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.StrengthenCostDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetSuitDisplayInfo", "RelativePath": "api/Equipment/suit-display/{equipId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipId", "Type": "System.String", "IsRequired": true}, {"Name": "petId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetPetSuitEffects", "RelativePath": "api/Equipment/suit-effects/{petId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.SuitEffectResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetSuitActivations", "RelativePath": "api/Equipment/suit/activation/{petNo}/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNo", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.SuitActivationDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetAllSuits", "RelativePath": "api/Equipment/suits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.SuitConfigDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetSuitById", "RelativePath": "api/Equipment/suits/{suitId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "suitId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.SuitConfigDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "UnequipFromPet", "RelativePath": "api/Equipment/unequip", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.UnequipFromPetRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "UnequipAllFromPet", "RelativePath": "api/Equipment/unequip-all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.UnequipAllFromPetRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "UnequipFromPetLegacy", "RelativePath": "api/Equipment/unequip-legacy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.UnequipRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetUserEquipments", "RelativePath": "api/Equipment/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.UserEquipmentDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "AddEquipment", "RelativePath": "api/Equipment/user/{userId}/add/{equipTypeId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "equipTypeId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentController", "Method": "GetUnusedEquipments", "RelativePath": "api/Equipment/user/{userId}/unused", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.UserEquipmentDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentAttributeController", "Method": "CalculateEquipmentValue", "RelativePath": "api/EquipmentAttribute/calculate-value", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.EquipmentValueCalculateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentAttributeController", "Method": "GetEnhancedAttributes", "RelativePath": "api/EquipmentAttribute/enhanced-attributes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.AttributeRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.ResultDTO.AttributeResultDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentAttributeController", "Method": "EnhancedBattleCalculate", "RelativePath": "api/EquipmentAttribute/enhanced-battle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.BattleRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.ResultDTO.BattleResultDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentAttributeController", "Method": "GetEquipmentAttributes", "RelativePath": "api/EquipmentAttribute/equipment/{userEquipmentId}/attributes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentAttributeController", "Method": "CompareAttributes", "RelativePath": "api/EquipmentAttribute/pet/{petNo}/user/{userId}/compare/{userEquipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNo", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Controllers.AttributeCompareResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentAttributeController", "Method": "GetDetailedAttributes", "RelativePath": "api/EquipmentAttribute/pet/{petNo}/user/{userId}/detailed-attributes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNo", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.AttributeCalculationResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentAttributeController", "Method": "GetPetEquipmentAttributes", "RelativePath": "api/EquipmentAttribute/pet/{petNo}/user/{userId}/equipment-attributes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNo", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "CheckGemstoneEmbed", "RelativePath": "api/EquipmentTest/check-embed/{userEquipmentId}/{gemstoneTypeName}/{position}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "gemstoneTypeName", "Type": "System.String", "IsRequired": true}, {"Name": "position", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "CleanupTestData", "RelativePath": "api/EquipmentTest/cleanup/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "TestConnection", "RelativePath": "api/EquipmentTest/connection", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "CreateTestEquipment", "RelativePath": "api/EquipmentTest/create-equipment/{userId}/{equipId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "equipId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "EmbedTestGemstone", "RelativePath": "api/EquipmentTest/embed-gemstone/{userEquipmentId}/{gemstoneTypeName}/{position}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "gemstoneTypeName", "Type": "System.String", "IsRequired": true}, {"Name": "position", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "GetEquipmentGemstones", "RelativePath": "api/EquipmentTest/equipment-gemstones/{userEquipmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userEquipmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.GemstoneDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "TestGemstones", "RelativePath": "api/EquipmentTest/gemstones", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.GemstoneConfigDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "GetStatistics", "RelativePath": "api/EquipmentTest/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.EquipmentTestController", "Method": "TestSuits", "RelativePath": "api/EquipmentTest/suits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.SuitConfigDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.GameController", "Method": "GetGameHomeData", "RelativePath": "api/Game/home/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.GameController", "Method": "GetGamePage", "RelativePath": "api/Game/page/{page}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.GameController", "Method": "GetPetInfoPageData", "RelativePath": "api/Game/pet-info/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.GameController", "Method": "GetPetDetail", "RelativePath": "api/Game/pet/{petId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.GameController", "Method": "SwitchPet", "RelativePath": "api/Game/switch-pet", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.SwitchPetRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.CompatibilityController", "Method": "GetPropList", "RelativePath": "api/getPropList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "password", "Type": "System.String", "IsRequired": false}, {"Name": "Type", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "BatchCalculateLevel", "RelativePath": "api/Level/BatchCalculateLevel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.BatchLevelCalculationRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.BatchLevelCalculationResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "CalculateLevel", "RelativePath": "api/Level/CalculateLevel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.LevelCalculationRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.LevelCalculationResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "CheckLevelUp", "RelativePath": "api/Level/CheckLevelUp", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "currentExp", "Type": "System.Int64", "IsRequired": false}, {"Name": "addExp", "Type": "System.Int64", "IsRequired": false}, {"Name": "systemName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "ClearLevelCache", "RelativePath": "api/Level/ClearLevelCache/clear-cache", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "GetLevelConfigs", "RelativePath": "api/Level/GetLevelConfigs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartLevel", "Type": "System.Int32", "IsRequired": false}, {"Name": "EndLevel", "Type": "System.Int32", "IsRequired": false}, {"Name": "SystemName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.LevelConfigListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "GetLevelConfigs", "RelativePath": "api/Level/GetLevelConfigs/configs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "systemName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.Models.LevelConfig, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "GetLevelProgress", "RelativePath": "api/Level/GetLevelProgress", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "experience", "Type": "System.Int64", "IsRequired": false}, {"Name": "systemName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.LevelProgressDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "GetSystemConfig", "RelativePath": "api/Level/GetSystemConfig", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "systemName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ExpSystemConfigDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "GetSystemConfigForDebug", "RelativePath": "api/Level/GetSystemConfigForDebug/system-config/{systemName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "systemName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.Models.ExpSystemConfig", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.LevelController", "Method": "ValidateExp", "RelativePath": "api/Level/ValidateExp", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "experience", "Type": "System.Int64", "IsRequired": false}, {"Name": "systemName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.MapController", "Method": "GetMapDetail", "RelativePath": "api/Map/{mapId}/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mapId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.MapDetailResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.MapController", "Method": "GetMapDrops", "RelativePath": "api/Map/{mapId}/drops", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mapId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.ResultDTO.MapDropDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.MapController", "Method": "EnterMap", "RelativePath": "api/Map/{mapId}/enter", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "mapId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.EnterMapRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.EnterMapResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.MapController", "Method": "GetMapMonsters", "RelativePath": "api/Map/{mapId}/monsters", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mapId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.ResultDTO.MapMonsterDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.MapController", "Method": "UpdateMapProgress", "RelativePath": "api/Map/{mapId}/progress", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "mapId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.UpdateMapProgressRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.UpdateMapProgressResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.MapController", "Method": "UnlockMap", "RelativePath": "api/Map/{mapId}/unlock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "mapId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.UnlockMapRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.UnlockMapResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.MapController", "Method": "GetMapList", "RelativePath": "api/Map/list/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.MapListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "GetConfigs", "RelativePath": "api/Nirvana/configs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.PetNirvanaConfigDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "GetUserCooldown", "RelativePath": "api/Nirvana/cooldown/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "ExecuteNirvana", "RelativePath": "api/Nirvana/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.NirvanaRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.NirvanaResultDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "ExecuteFaceChange", "RelativePath": "api/Nirvana/face-change", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.FaceChangeRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.FaceChangeResultDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "GetHelp", "RelativePath": "api/Nirvana/help", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "GetUserNirvanaBeasts", "RelativePath": "api/Nirvana/nirvana-beasts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "GetUserNirvanaBeastsByUserId", "RelativePath": "api/Nirvana/nirvana-beasts/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "PreviewNirvana", "RelativePath": "api/Nirvana/preview", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.NirvanaPreviewRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.NirvanaPreviewDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "GetUserRecords", "RelativePath": "api/Nirvana/records/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "size", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Utils.PagedResult`1[[WebApplication_HM.DTOs.NirvanaRecordDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "GetStatistics", "RelativePath": "api/Nirvana/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.NirvanaStatisticsDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.NirvanaController", "Method": "ValidateNirvana", "RelativePath": "api/Nirvana/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.NirvanaRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PetEvolutionController", "Method": "GetEvolutionHistory", "RelativePath": "api/PetEvolution/{userPetId}/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userPetId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetEvolutionController", "Method": "GetPetEvolutionInfo", "RelativePath": "api/PetEvolution/{userPetId}/info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userPetId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetEvolutionController", "Method": "GetBatchPetEvolutionInfo", "RelativePath": "api/PetEvolution/batch-info", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userPetIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetEvolutionController", "Method": "GetEvolutionConfig", "RelativePath": "api/PetEvolution/config/{petNo}/{evolutionType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNo", "Type": "System.Int32", "IsRequired": true}, {"Name": "evolutionType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetEvolutionController", "Method": "EvolvePet", "RelativePath": "api/PetEvolution/evolve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.EvolutionRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "BatchOperation", "RelativePath": "api/PetManagement/batch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.PetManagement.BatchPetOperationRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "GetPastureCapacity", "RelativePath": "api/PetManagement/capacity/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "CarryPet", "RelativePath": "api/PetManagement/carry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.PetManagement.CarryPetRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "GetPetDetail", "RelativePath": "api/PetManagement/detail/{userId}/{petId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "DiscardPet", "RelativePath": "api/PetManagement/discard", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.PetManagement.DiscardPetRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "GetMigrationReport", "RelativePath": "api/PetManagement/migration-report/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "GetPasturePets", "RelativePath": "api/PetManagement/pasture/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "RenamePet", "RelativePath": "api/PetManagement/rename", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.PetManagement.RenamePetRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "SetMainPet", "RelativePath": "api/PetManagement/setMain", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.PetManagement.SetMainPetRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetManagementController", "Method": "StorePet", "RelativePath": "api/PetManagement/store", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.PetManagement.StorePetRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetSynthesisController", "Method": "GetSynthesisConfig", "RelativePath": "api/PetSynthesis/{mainPetId}/{vicePetId}/config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mainPetId", "Type": "System.Int32", "IsRequired": true}, {"Name": "vicePetId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetSynthesisController", "Method": "ValidateSynthesisConditions", "RelativePath": "api/PetSynthesis/{mainPetId}/{vicePetId}/validate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mainPetId", "Type": "System.Int32", "IsRequired": true}, {"Name": "vicePetId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetSynthesisController", "Method": "GetGodPetProbability", "RelativePath": "api/PetSynthesis/god-pet-probability/{currentGrowth}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "current<PERSON><PERSON>th", "Type": "System.Decimal", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetSynthesisController", "Method": "GetSynthesisHistory", "RelativePath": "api/PetSynthesis/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetSynthesisController", "Method": "GetSynthesisStatistics", "RelativePath": "api/PetSynthesis/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetSynthesisController", "Method": "SynthesizePet", "RelativePath": "api/PetSynthesis/synthesize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.SynthesisRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetSynthesisController", "Method": "GetVipBonus", "RelativePath": "api/PetSynthesis/vip-bonus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "GetTransformConfig", "RelativePath": "api/PetTransform/config/{transformType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "transformType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "CheckCooldown", "RelativePath": "api/PetTransform/cooldown/{userId}/{transformType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "transformType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "GetGodPets", "RelativePath": "api/PetTransform/god-pets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "GetHolyPets", "RelativePath": "api/PetTransform/holy-pets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "InitializeDefaultConfig", "RelativePath": "api/PetTransform/initialize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "RandomGenerateGodPet", "RelativePath": "api/PetTransform/random-god-pet", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RandomGodPetRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "GetTransformSelectionData", "RelativePath": "api/PetTransform/selection-data/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "GetUserTransformStats", "RelativePath": "api/PetTransform/stats/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "TransformPet", "RelativePath": "api/PetTransform/transform", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.PetTransformRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "GetTransformablePets", "RelativePath": "api/PetTransform/transformable-pets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PetTransformController", "Method": "ValidateTransformablePet", "RelativePath": "api/PetTransform/validate/{petNo}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNo", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetGameConfig", "RelativePath": "api/Player", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.ConfigRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.GameConfigResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "BattleCalculate", "RelativePath": "api/Player/BattleCalculate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.BattleRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.BattleResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "CalculateBattle", "RelativePath": "api/Player/CalculateBattle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.BattleRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.BattleResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "ClaimBattleReward", "RelativePath": "api/Player/ClaimBattleReward", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.ClaimRewardRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.ClaimRewardResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "EquipItem", "RelativePath": "api/Player/EquipItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.EquipRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.LoginResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetEvolutionAvailablePets", "RelativePath": "api/Player/evolution-available", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetBag", "RelativePath": "api/Player/GetBag", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.BagRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.BagResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetCarryPets", "RelativePath": "api/Player/GetCarryPets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.PetListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetCurrentUser", "RelativePath": "api/Player/GetCurrentUser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetEquipmentConfigs", "RelativePath": "api/Player/GetEquipmentConfigs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.GameConfigResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetItemConfigs", "RelativePath": "api/Player/GetItemConfigs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.GameConfigResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetMainPet", "RelativePath": "api/Player/GetMainPet", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.BattlePetInfoResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetMainPetList", "RelativePath": "api/Player/GetMainPetList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.PetListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetMapDetail", "RelativePath": "api/Player/GetMapDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.MapDetailRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.MapDetailResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetMapList", "RelativePath": "api/Player/GetMapList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.MapListRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.MapListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetMonsterConfigs", "RelativePath": "api/Player/GetMonsterConfigs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.GameConfigResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetPetAttributes", "RelativePath": "api/Player/GetPetAttributes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.AttributeRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.AttributeResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetPetAttributesGet", "RelativePath": "api/Player/GetPetAttributes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}, {"Name": "petId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.AttributeResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetPetConfigs", "RelativePath": "api/Player/GetPetConfigs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.GameConfigResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetPetDetail", "RelativePath": "api/Player/GetPetDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.PetDetailRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.PetDetailResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetPlayerInfo", "RelativePath": "api/Player/GetPlayerInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.PlayerInfoRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.PlayerInfoResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetRanchPets", "RelativePath": "api/Player/GetRanchPets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.PetListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetRealmConfigs", "RelativePath": "api/Player/GetRealmConfigs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.GameConfigResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetSkillConfigs", "RelativePath": "api/Player/GetSkillConfigs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.GameConfigResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetUserEquipments", "RelativePath": "api/Player/GetUserEquipments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.EquipmentListRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.EquipmentListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetUserPets", "RelativePath": "api/Player/GetUserPets", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.PetListRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.PetListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetUserPetsByStatus", "RelativePath": "api/Player/GetUserPetsByStatus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "only<PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.PetListResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Player/Login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.ResultDTO.LoginRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.LoginResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "Logout", "RelativePath": "api/Player/Logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetNirvanaAvailablePets", "RelativePath": "api/Player/nirvana-available", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "Register", "RelativePath": "api/Player/Register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.RegisterRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.RegisterResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "SellItem", "RelativePath": "api/Player/SellItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.SellItemRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.SellItemResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "SetMainPet", "RelativePath": "api/Player/SetMainPet", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.SetMainPetRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.LoginResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "SortBag", "RelativePath": "api/Player/SortBag", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.SortBagRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.SortBagResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "GetSynthesisAvailablePets", "RelativePath": "api/Player/synthesis-available", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "UnequipItem", "RelativePath": "api/Player/UnequipItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.UnequipRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.LoginResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PlayerController", "Method": "UseItem", "RelativePath": "api/Player/UseItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.UseItemRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.ResultDTO.UseItemResultDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropScriptController", "Method": "ExecuteMultiScriptSelection", "RelativePath": "api/prop-script/multi-select", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Services.PropScript.MultiScriptSelectRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.PropScript.PropScriptResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropScriptController", "Method": "GetUserMultiScriptState", "RelativePath": "api/prop-script/multi-state/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.PropScript.MultiScriptState, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropScriptController", "Method": "ClearUserMultiScriptState", "RelativePath": "api/prop-script/multi-state/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropScriptController", "Method": "GetSupportedScriptTypes", "RelativePath": "api/prop-script/script-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.Controllers.ScriptTypeInfo, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropScriptController", "Method": "TestScript", "RelativePath": "api/prop-script/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.ScriptTestRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.PropScript.PropScriptResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropScriptController", "Method": "UseItemWithScript", "RelativePath": "api/prop-script/use", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Services.PropScript.PropScriptExecuteRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.PropScript.PropScriptResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropScriptController", "Method": "ValidateScript", "RelativePath": "api/prop-script/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "script", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.Services.PropScript.ScriptValidationResult, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "DeleteItem", "RelativePath": "api/Prop/{itemSeq}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemSeq", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "MoveToBackpack", "RelativePath": "api/Prop/{itemSeq}/move-to-backpack", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemSeq", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "AddUserItem", "RelativePath": "api/Prop/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Models.AddItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetItemConfig", "RelativePath": "api/Prop/config/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.Models.PropConfig", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "DiscardItem", "RelativePath": "api/Prop/discard", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Models.DiscardItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetDiscardLogs", "RelativePath": "api/Prop/discard-logs/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "MoveItem", "RelativePath": "api/Prop/move", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Models.MoveItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetNirvanaMaterials", "RelativePath": "api/Prop/nirvana-materials", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "RecoverDiscardedItem", "RelativePath": "api/Prop/recover/{logId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetItemScript", "RelativePath": "api/Prop/script/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.Models.PropScriptInfo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetItemBySeq", "RelativePath": "api/Prop/seq/{itemSeq}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemSeq", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.Models.PropInfo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetSynthesisMaterials", "RelativePath": "api/Prop/synthesis-materials", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "UseItem", "RelativePath": "api/Prop/use", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Models.UseItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetUserItems", "RelativePath": "api/Prop/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.Models.PropInfo, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "HasItem", "RelativePath": "api/Prop/user/{userId}/has/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "itemId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetUserItemById", "RelativePath": "api/Prop/user/{userId}/item/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "itemId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.Models.PropInfo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.PropController", "Method": "GetUserItemsByPosition", "RelativePath": "api/Prop/user/{userId}/position/{position}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "position", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.Models.PropInfo, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealmController", "Method": "GetBatchRealmInfo", "RelativePath": "api/Realm/batch-info", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "petIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.RealmController", "Method": "GetRealmAttributeBonus", "RelativePath": "api/Realm/bonus/{petId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.RealmController", "Method": "BreakthroughRealm", "RelativePath": "api/Realm/breakthrough", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.RealmUpgradeRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.RealmController", "Method": "GetRealmHistory", "RelativePath": "api/Realm/history", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.GetRealmHistoryRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.RealmController", "Method": "GetRealmInfo", "RelativePath": "api/Realm/info", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.GetRealmInfoRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.RealmController", "Method": "GetRealmList", "RelativePath": "api/Realm/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.RealmController", "Method": "UpgradeRealm", "RelativePath": "api/Realm/upgrade", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.RealmUpgradeRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "BroadcastMessage", "RelativePath": "api/RealTime/BroadcastMessage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.BroadcastMessageRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "GetOnlinePlayerCount", "RelativePath": "api/RealTime/GetOnlinePlayerCount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "GetOnlinePlayers", "RelativePath": "api/RealTime/GetOnlinePlayers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.ResultDTO.PlayerStatusDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "SendGameEvent", "RelativePath": "api/RealTime/SendGameEvent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.GameEventRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "SendHeartbeat", "RelativePath": "api/RealTime/SendHeartbeat", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "playerId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "SendPrivateMessage", "RelativePath": "api/RealTime/SendPrivateMessage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.PrivateMessageRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "SendSystemNotice", "RelativePath": "api/RealTime/SendSystemNotice", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.SystemNoticeRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.RealTimeController", "Method": "UpdatePlayerStatus", "RelativePath": "api/RealTime/UpdatePlayerStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.RequestDTO.PlayerStatusUpdateRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetAllSkills", "RelativePath": "api/Skill/configs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.SkillConfigDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetSkillById", "RelativePath": "api/Skill/configs/{skillId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skillId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.SkillConfigDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetSkillsByElement", "RelativePath": "api/Skill/configs/element/{element}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "element", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.SkillConfigDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetSkillsByType", "RelativePath": "api/Skill/configs/type/{skillType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skillType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.SkillConfigDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetPetActiveSkill", "RelativePath": "api/Skill/pet/{petId}/active-skill", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.PetSkillDetailDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "CanLearnSkill", "RelativePath": "api/Skill/pet/{petId}/can-learn/{skillId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "skillId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "CanUpgradeSkill", "RelativePath": "api/Skill/pet/{petId}/can-upgrade/{skillId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "skillId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "ForgetSkill", "RelativePath": "api/Skill/pet/{petId}/forget", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WebApplication_HM.DTOs.ForgetSkillRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "LearnSkill", "RelativePath": "api/Skill/pet/{petId}/learn", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WebApplication_HM.DTOs.LearnSkillRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.SkillLearnResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "CalculatePassiveEffects", "RelativePath": "api/Skill/pet/{petId}/passive-effects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetPetPassiveSkills", "RelativePath": "api/Skill/pet/{petId}/passive-skills", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.PetSkillDetailDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "CalculateSkillEffects", "RelativePath": "api/Skill/pet/{petId}/skill-effects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "activeSkillId", "Type": "System.String", "IsRequired": false}, {"Name": "currentMana", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.SkillEffectResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "CalculateManaCost", "RelativePath": "api/Skill/pet/{petId}/skill/{skillId}/mana-cost", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "skillId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "CalculateSkillMultiplier", "RelativePath": "api/Skill/pet/{petId}/skill/{skillId}/multiplier", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "skillId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Double", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetPetSkills", "RelativePath": "api/Skill/pet/{petId}/skills", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.PetSkillDetailDTO, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "GetPetSkillStatistics", "RelativePath": "api/Skill/pet/{petId}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.Interface.PetSkillStatistics", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SkillController", "Method": "UpgradeSkill", "RelativePath": "api/Skill/pet/{petId}/upgrade", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "petId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WebApplication_HM.DTOs.UpgradeSkillRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.SkillUpgradeResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.SynthesisValidPetsController", "Method": "GetAllValidPets", "RelativePath": "api/SynthesisValidPets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.SynthesisValidPetsController", "Method": "ValidatePet", "RelativePath": "api/SynthesisValidPets/{petNo}/validate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNo", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.SynthesisValidPetsController", "Method": "GetValidPetsCount", "RelativePath": "api/SynthesisValidPets/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.SynthesisValidPetsController", "Method": "InitializeDefaultData", "RelativePath": "api/SynthesisValidPets/initialize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.SynthesisValidPetsController", "Method": "RefreshCache", "RelativePath": "api/SynthesisValidPets/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.SynthesisValidPetsController", "Method": "ValidateMultiplePets", "RelativePath": "api/SynthesisValidPets/validate-batch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "petNos", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "AbandonTask", "RelativePath": "api/Task/abandon", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.AbandonTaskRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.TaskOperationResultDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "AcceptTask", "RelativePath": "api/Task/accept", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.AcceptTaskRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.TaskOperationResultDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "GetAcceptedTasks", "RelativePath": "api/Task/accepted/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.TaskInfoDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "RefreshUserTaskStatus", "RelativePath": "api/Task/admin/refresh/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "TriggerGameEvent", "RelativePath": "api/Task/admin/trigger-event", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.Controllers.TriggerEventRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "CheckAndAutoCompleteTasks", "RelativePath": "api/Task/auto-complete/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.UserTaskDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "GetAvailableTasks", "RelativePath": "api/Task/available/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.TaskInfoDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "BatchUpdateTaskProgress", "RelativePath": "api/Task/batch-update-progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "events", "Type": "System.Collections.Generic.List`1[[WebApplication_HM.DTOs.TaskProgressUpdateEventDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "CheckTaskAcceptability", "RelativePath": "api/Task/check-acceptability/{userId}/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "CompleteTask", "RelativePath": "api/Task/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.CompleteTaskRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.TaskOperationResultDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "GetTaskDetail", "RelativePath": "api/Task/detail/{userId}/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.TaskDetailResponseDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "GetInProgressTasks", "RelativePath": "api/Task/in-progress/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Collections.Generic.List`1[[WebApplication_HM.DTOs.UserTaskDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "GetTaskList", "RelativePath": "api/Task/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.TaskListRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[WebApplication_HM.DTOs.TaskListResponseDto, WebApplication_HM, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "RefreshTaskStatus", "RelativePath": "api/Task/refresh/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "GetRewardHistory", "RelativePath": "api/Task/reward-history/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TaskController", "Method": "UpdateTaskProgress", "RelativePath": "api/Task/update-progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApplication_HM.DTOs.UpdateTaskProgressRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApplication_HM.DTOs.Common.ApiResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApplication_HM.Controllers.TestController", "Method": "TestConnection", "RelativePath": "api/Test/connection", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.TestController", "Method": "GetItemConfigs", "RelativePath": "api/Test/item-configs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.TestController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Test/join-test/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.TestController", "Method": "GetUserItemsRaw", "RelativePath": "api/Test/user-items/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApplication_HM.Controllers.CompatibilityController", "Method": "UseProp", "RelativePath": "api/useProp", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "password", "Type": "System.String", "IsRequired": false}, {"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_9", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain"], "StatusCode": 200}]}]