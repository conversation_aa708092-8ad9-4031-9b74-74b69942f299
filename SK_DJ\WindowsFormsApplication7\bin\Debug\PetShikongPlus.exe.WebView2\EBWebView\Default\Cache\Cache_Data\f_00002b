<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>地图详情</title>
    <script src="../Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="../../js/game-api-adapter.js"></script>
    <script src="../../js/pet-info-api-adapter.js"></script>
    
    <style type="text/css"> 
        body { width: auto; height: auto; margin: 0px auto; font-size: 12px; background:#eeeeee;}
        *{ margin: 0px; padding: 0px; }
        pre { margin: 0px; padding: 0px; float: left; }
        img { margin: 0px; padding: 0px; border: 0px; }
        ul { margin: 0px; padding: 0px; }
        li { display: inline; list-style-type: none; }
        tr td{
            float:left;
            margin-right:10px;
        }
        .zdzd_box { width:788px; height:319px; float:left;}
        .box_left { width:301px; height:319px; float:left; background:url(img/zdzd_bj1.jpg);}
        .box_zhong { width:283px; height:319px; float:left; background:url(img/zdzd_bj2.jpg);}
        .box_right { width:204px; height:319px; float:left; background:url(img/zdzd_bj3.jpg);}
        .zdzd_jiesao { width:230px; height:90px; float:left; margin:70px 0px 0px 40px; float:left; line-height:22px; overflow:auto;}
        .zdzd_cong { width:247px; height:84px; float:left; margin:50px 0px 0px 10px; line-height:22px;}
        .zhong_list { width:260px; height:210px; float:left; margin:0px 0px 0px 5px;line-height:22px; margin-top:59px; color:#335e00; overflow:hidden;}
        .zhong_list a { text-decoration:none; color:#335e00;}
        .zhong_list a:hover { text-decoration:none; color:#335e00;}
        .zhong_list li { width:240px; height:26px; float:left;line-height:27px; display:inline;}
        .title { width:120px; height:22px; float:left;line-height:22px; overflow:hidden;}
        .title2 { width:35px; height:22px; float:left;line-height:22px;}
        .title3 { width:69px; height:17px; float:left;line-height:22px; text-align:center; margin-top:2px;}
        .anniu { width:265px; height:29px; float:left; margin:15px 0px 0px 5px;}
        .anniu1 { width:80px; height:29px; float:left; margin:0px 0px 0px 7px;}
        .zdzd_zxwj { width:170px; height:260px; float:left; margin:44px 0px 0px 10px; color:#335e00; overflow:auto; line-height:26px;}
        .zdzd_zxwj a{ text-decoration:none; color:#335e00;}
        .zdzd_zxwj a:hover{ text-decoration:none; color:#335e00;}
        .zxwj_list  { width:30px; height:26px; line-height:26px; float:left;}
        .zxwj_list img { margin:5px 0px 0px 4px;}
        .zxwj_list2  { width:120px; height:26px; line-height:26px; float:left;}
        
        .yy {
            cursor:pointer;
            filter:alpha(opacity=50);
            opacity:0.5;
        }
        
        .pet-selected {
            cursor:pointer;
            filter:alpha(opacity=100);
            opacity:1.0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 20px;
            color: #ff0000;
        }
    </style>
</head>

<body>
    <div class="zdzd_box">
        <!-- 左侧：地图信息和宠物选择 -->
        <div class="box_left">
            <div class="zdzd_jiesao" id="mapDescription">
                <div class="loading">正在加载地图信息...</div>
            </div>
            <div class="zdzd_cong">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr id="petSelection">
                        <td width="5">123&nbsp;</td>
                        <td class="loading">正在加载宠物...</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 中间：组队信息和操作按钮 -->
        <div class="box_zhong">
            <div class="zhong_list" id="teamList">
                <table border="0" style="border-bottom:1px solid #CCCCCC; color:#005500">
                    <tr>
                        <td align="center" height="21" width="95">队长</td>
                        <td align="center" width="50">队员人数</td>
                        <td align="center" width="80">申请加入</td>
                    </tr>
                </table>
                <div id="teamContent">
                    <div class="loading">正在加载组队信息...</div>
                </div>
            </div>
            
            <div class="anniu">
                <div class="anniu1">
                    <a href="javascript:void(0)" onclick="startBattle()">
                        <img src="img/zd.gif" alt="战斗" />
                    </a>
                </div>
                <div class="anniu1">
                    <a href="javascript:void(0)" onclick="createTeam()">
                        <img src="img/cjdw.gif" alt="创建队伍" />
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 右侧：在线玩家 -->
        <div class="box_right">
            <div class="zdzd_zxwj" id="onlinePlayers">
                <div class="loading">正在加载在线玩家...</div>
            </div>
        </div>
    </div>
    
    <script type="text/javascript">
        let currentMapId = null;
        let currentMapData = null;
        let selectedPetId = null;
        let userPets = [];
        
        // 页面加载时初始化
        $(document).ready(function() {
            console.log('MapInfo页面加载完成');
            
            // 从URL获取地图ID
            currentMapId = getMapIdFromUrl();
            console.log('当前地图ID:', currentMapId);
            
            if (currentMapId) {
                loadMapData();
                loadUserPets();
                loadTeamInfo();
                loadOnlinePlayers();
            } else {
                showError('无效的地图ID');
            }
        });
        
        // 从URL获取地图ID（兼容性增强版）
        function getMapIdFromUrl() {
            // 优先从URL参数获取
            const urlParams = new URLSearchParams(window.location.search);
            const mapId = urlParams.get('mapId');
            if (mapId) {
                return parseInt(mapId);
            }
            
            // 兼容旧版本：从文件名获取
            const path = window.location.pathname;
            const match = path.match(/t(\d+)\.html$/);
            return match ? parseInt(match[1]) : null;
        }
        
        // 加载地图数据
        async function loadMapData() {
            try {
                console.log('开始加载地图数据...');
                const userId = parent.gameAPI.getCurrentUserId();
                const result = await parent.gameAPI.getMapDetail(currentMapId, userId);
                console.log('地图API响应:', result);
                
                if (result.success) {
                    currentMapData = result;
                    renderMapInfo();
                } else {
                    console.error('获取地图信息失败:', result.message);
                    $('#mapDescription').html('<div class="error">加载地图信息失败: ' + result.message + '</div>');
                }
            } catch (error) {
                console.error('加载地图数据失败:', error);
                $('#mapDescription').html('<div class="error">网络错误</div>');
            }
        }
        
        // 渲染地图信息
        function renderMapInfo() {
            const { mapInfo, monsters, drops } = currentMapData;
            
            let content = `<div><strong>${mapInfo.mapName}</strong></div>`;
            
            // 怪物信息
            if (monsters && monsters.length > 0) {
                content += '<div><strong>出现怪物：</strong></div>';
                monsters.forEach(monster => {
                    content += `<div>${monster.monsterName} (${monster.levelRange}) [${monster.element}]</div>`;
                });
            } else {
                content += '<div><strong>出现怪物：</strong>暂无怪物信息</div>';
            }
            
            // 奖励信息
            if (drops && drops.length > 0) {
                content += '<div><strong>可能掉落：</strong></div>';
                drops.forEach(drop => {
                    content += `<div>${drop.itemName} (${(drop.dropRate * 100).toFixed(1)}%)</div>`;
                });
            }
            
            $('#mapDescription').html(content);
        }
        
        // 加载用户宠物
        async function loadUserPets() {
            try {
                console.log('开始加载用户宠物...');
                const userId = parent.gameAPI.getCurrentUserId();
                const result = await parent.gameAPI.getUserPets(userId);
                console.log('宠物API响应:', result);

                if (result.success) {
                    userPets = result.pets || [];
                    console.log('获取到宠物数据:', userPets);
                    renderPetSelection();
                } else {
                    console.error('获取宠物失败:', result.message);
                    $('#petSelection').html('<tr><td colspan="5">加载宠物失败: ' + result.message + '</td></tr>');
                }
            } catch (error) {
                console.error('加载用户宠物失败:', error);
                $('#petSelection').html('<tr><td colspan="5">网络错误</td></tr>');
            }
        }

        // 渲染宠物选择（兼容性增强版 - B方案）
        function renderPetSelection() {
            console.log('渲染宠物选择，宠物数量:', userPets.length);
            console.log('宠物数据示例:', userPets[0]);

            // 兼容性处理：如果没有宠物数据，显示默认宠物（模拟原版写死的效果）
            if (userPets.length === 0) {
                console.log('没有宠物数据，使用默认宠物显示');

                return;
            }

            // 动态渲染用户实际宠物（修复字段名匹配API返回）
            let html = '<tr id="bottom"><td width="5">&nbsp;</td>';

            userPets.slice(0, 3).forEach((pet, index) => {
                // 使用正确的字段名（小写，匹配API返回）
                const isSelected = selectedPetId === pet.id;
                const isMain = pet.isMain || pet.isMainPet; // 兼容两种字段名
                const opacity = isSelected ? 100 : (isMain ? 100 : 50);
                const petImage = pet.petNo || pet.image || 264;
                const petName = pet.name || '未知宠物';
                const petNo=pet.petNo;
                const petid=pet.id;    
                html += `
                    <td class="${isSelected ? 'pet-selected' : 'yy'}" onclick="selectPet(${petid}, ${index})">
                        <img src="../Content/PetPhoto/k${petNo}.gif"
                             alt="${petName}"
                             title="${petName} (Lv.${pet.level})"
                             onerror="this.src='../Content/PetPhoto/k${petNo}.png'"
                             style="cursor:pointer;filter:alpha(opacity=${opacity});opacity:${opacity/100};"
                             id="pet${index}">
                    </td>
                `;
            });

            // 填充空位（保持与原版t4.html一致的布局）
            for (let i = userPets.length; i < 3; i++) {
                html += '<td></td>';
            }

            html += '<td width="20">&nbsp;</td></tr>';
            $('#petSelection').html(html);

            // 默认选择主战宠物或第一个宠物
            if (!selectedPetId && userPets.length > 0) {
                const mainPet = userPets.find(pet => pet.isMain || pet.isMainPet);
                if (mainPet) {
                    selectPet(mainPet.id, userPets.indexOf(mainPet));
                } else {
                    selectPet(userPets[0].id, 0);
                }
            }
        }
        
        // 选择宠物（兼容性增强版）- 参考PetInfo.html的handlePetClick实现
        function selectPet(petId, index) {
            console.log('选择宠物:', petId, index);
            selectedPetId = petId;

            // 更新宠物显示样式
            updatePetDisplayStyles(petId, index);

             try {
                const userId = getCurrentUserId();

                const response = fetch(`/api/Game/switch-pet`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId, petId: parseInt(petId) })
                });

                const result = response.json();

                if (!result.success) {
                    // 只在失败时显示错误提示
                    console.error('切换宠物失败:', result.message);
                  
                }
            } catch (error) {
                console.error('切换宠物时发生错误:', error);
              
            }

        }

       
        /**
         * 更新宠物显示样式
         * @param {string|number} petId - 选中的宠物ID
         * @param {number} index - 宠物在列表中的索引位置
         */
        function updatePetDisplayStyles(petId, index) {
            // 更新所有宠物的透明度和样式
            userPets.forEach((pet, i) => {
                if (pet) {
                    const isSelected = (pet.id === petId);
                    const opacity = isSelected ? 100 : 50;
                    const petElement = $(`#pet${i}`);
                    const tdElement = petElement.parent();

                    petElement.css({
                        'filter': `alpha(opacity=${opacity})`,
                        'opacity': opacity / 100
                    });

                    if (isSelected) {
                        tdElement.removeClass('yy').addClass('pet-selected');
                    } else {
                        tdElement.removeClass('pet-selected').addClass('yy');
                    }
                }
            });

            console.log(`宠物显示样式已更新，选中宠物ID: ${petId}`);
        }

        // 选择默认宠物（兼容性：用于默认宠物模式）
        function selectDefaultPet(petId, index) {
            console.log('选择默认宠物:', petId, index);

            // 调用异步函数切换主战宠物，但不等待结果（避免阻塞UI）
            switchDefaultMainPet(petId, index).catch(error => {
                console.error('默认宠物切换失败:', error);
                // 显示错误提示
                if (window.loadingManager) {
                    window.loadingManager.showError('宠物切换失败，请重试');
                } else {
                    alert('宠物切换失败，请重试');
                }
            });
        }

        /**
         * 切换默认主战宠物函数
         * @param {string|number} petId - 宠物ID
         * @param {number} index - 宠物在列表中的索引位置
         */
        async function switchDefaultMainPet(petId, index) {
            selectedPetId = petId;

            console.log(`开始切换默认主战宠物，ID: ${petId}, 索引: ${index}`);

            // 先调用API设置主战宠物
            let switchSuccess = false;
            if (typeof window.external !== 'undefined' && typeof window.external.switchPet === 'function') {
                try {
                    console.log('调用API设置主战宠物...');
                    switchSuccess = await window.external.switchPet(petId);

                    if (switchSuccess) {
                        console.log('主战宠物设置成功');
                    } else {
                        console.log('主战宠物设置失败');
                        return;
                    }
                } catch (error) {
                    console.error('设置主战宠物时发生错误:', error);
                    throw error;
                }
            } else {
                console.log('switchPet API不可用，仅更新显示');
                switchSuccess = true;
            }

            // 如果设置主战宠物成功，更新页面显示
            if (switchSuccess) {
                updateDefaultPetDisplayStyles(petId, index);
            }
        }

        /**
         * 更新默认宠物显示样式
         * @param {string|number} petId - 选中的宠物ID
         * @param {number} index - 宠物在列表中的索引位置
         */
        function updateDefaultPetDisplayStyles(petId, index) {
            // 更新默认宠物的透明度和样式
            for (let i = 0; i < 2; i++) { // 默认只有2个宠物
                const opacity = (i === index) ? 100 : 50;
                const petElement = $(`#pet${i}`);
                const tdElement = petElement.parent();

                petElement.css({
                    'filter': `alpha(opacity=${opacity})`,
                    'opacity': opacity / 100
                });

                if (i === index) {
                    tdElement.removeClass('yy').addClass('pet-selected');
                } else {
                    tdElement.removeClass('pet-selected').addClass('yy');
                }
            }

            console.log(`默认宠物显示样式已更新，选中宠物ID: ${petId}`);
        }

        // 加载组队信息（简化版）
        function loadTeamInfo() {
            $('#teamContent').html('<div style="text-align: center; padding: 20px; color: #666;">队伍功能开发中...</div>');
        }

        // 加载在线玩家（简化版）
        function loadOnlinePlayers() {
            $('#onlinePlayers').html('<div style="text-align: center; padding: 20px; color: #666;">在线玩家功能开发中...</div>');
        }

        // 开始战斗
        function startBattle() {
            console.log('[MapInfo] 开始战斗函数被调用');

            // 验证必要条件
            if (!currentMapId) {
                alert('地图信息未加载，请刷新页面重试！');
                console.error('[MapInfo] 地图ID未设置:', currentMapId);
                return;
            }

            if (!selectedPetId) {
                alert('请先选择一个宠物！');
                console.warn('[MapInfo] 未选择宠物');
                return;
            }

            // 验证宠物是否有效
            const selectedPet = userPets.find(pet => pet.id === selectedPetId);
            if (!selectedPet) {
                alert('选择的宠物无效，请重新选择！');
                console.error('[MapInfo] 选择的宠物无效:', selectedPetId);
                return;
            }

            console.log('[MapInfo] 战斗参数验证通过:', {
                mapId: currentMapId,
                petId: selectedPetId,
                petName: selectedPet.name,
                petLevel: selectedPet.level
            });

            // 设置全局变量供Battle页面使用
            if (typeof parent !== 'undefined') {
                parent.selectedPetId = selectedPetId;
                parent.currentMapId = currentMapId;
                parent.selectedPet = selectedPet;

                console.log('[MapInfo] 已设置parent全局变量:', {
                    selectedPetId: parent.selectedPetId,
                    currentMapId: parent.currentMapId,
                    selectedPet: parent.selectedPet
                });
            }

            // 构建Battle页面URL，传递地图ID和宠物ID
            const battleUrl = `../Battle.html?mapId=${currentMapId}&petId=${selectedPetId}`;
            console.log('[MapInfo] 跳转到战斗页面:', battleUrl);

            // 跳转到战斗页面
            try {
                window.location.href = battleUrl;
            } catch (error) {
                console.error('[MapInfo] 页面跳转失败:', error);
                alert('跳转到战斗页面失败，请重试！');
            }
        }

        // 创建队伍
        function createTeam() {
            console.log('创建队伍');
            alert('创建队伍功能开发中...');
        }

        // 显示错误信息
        function showError(message) {
            $('#mapDescription').html('<div class="error">' + message + '</div>');
            $('#petSelection').html('<tr><td colspan="5"><div class="error">' + message + '</div></td></tr>');
        }

        // 页面加载完成后的初始化检查
        $(document).ready(function() {
            console.log('MapInfo页面加载完成，检查API状态...');
            console.log('API检查结果:', {
                'window.external': typeof window.external,
                'window.external.switchPet': typeof window.external?.switchPet,
                'window.gameAPI': typeof window.gameAPI,
                'window.gameAPI.switchPet': typeof window.gameAPI?.switchPet
            });

            // 延迟检查，确保所有脚本都已加载
            setTimeout(() => {
                console.log('延迟API检查结果:', {
                    'window.external': typeof window.external,
                    'window.external.switchPet': typeof window.external?.switchPet,
                    'window.gameAPI': typeof window.gameAPI,
                    'window.gameAPI.switchPet': typeof window.gameAPI?.switchPet
                });
            }, 1000);
        });
    </script>

<!-- Visual Studio Browser Link -->
<script type="text/javascript" src="/_vs/browserLink" async="async" id="__browserLink_initializationData" data-requestId="bd2ebd2169574110b5d9a5b4d70375f4" data-requestMappingFromServer="false" data-connectUrl="http://localhost:52634/583a38e14bff42bb96e5f3acc057bea3/browserLink"></script>
<!-- End Browser Link -->
</body>
</html>
