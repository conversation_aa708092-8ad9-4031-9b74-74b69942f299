
<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>宠物神殿</title>
<link rel="stylesheet" type="text/css" href="Content/petMain/css/reset.css">
<link href="Content/petMain/css/mcsd.css" rel="stylesheet">
<script src="jquery-1.8.3.min.js"></script>
<script src="../js/auth-manager.js"></script>
<script src="../js/pet-synthesis-api-adapter.js"></script>

<style>

.sd_pet{
		cursor: pointer;}
		.yy {
	cursor:hand;
	opacity: 0.5;
    filter : progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=50, finishOpacity=100);
}

/* 宠物选中状态样式 */
.sd_pet.selected {
	border: 2px solid #ff6600 !important;
	background-color: rgba(255, 102, 0, 0.1) !important;
	border-radius: 5px;
}

/* 调试样式 */
.宠物背包 {
	border: 1px dashed #ccc;
	min-height: 50px;
	padding: 5px;
}
</style>

<script type="text/javascript">
		var sxcw="";

		/**
		 * 获取当前用户ID
		 * 优先从认证管理器获取，如果未登录则使用测试用户ID
		 * @returns {number} 用户ID
		 */
		function getCurrentUserId() {
			// 优先从认证管理器获取
			if (window.authManager && window.authManager.isLoggedIn()) {
				const userId = window.authManager.getCurrentUserId();
				if (userId) {
					console.log(`✅ 从认证管理器获取用户ID: ${userId}`);
					return userId;
				}
			}

			// 备用方案：从全局函数获取
			if (typeof window.getCurrentUserId === 'function') {
				const userId = window.getCurrentUserId();
				if (userId && userId !== 1) {
					console.log(`✅ 从全局函数获取用户ID: ${userId}`);
					return userId;
				}
			}

			// 检查是否为开发环境
			const isDev = window.location.hostname === 'localhost' ||
						 window.location.hostname === '127.0.0.1' ||
						 window.location.hostname.includes('dev');

			if (isDev) {
				console.warn('⚠️ 开发环境：未能获取到有效用户ID，使用测试用户ID: 1');
				return 1;
			} else {
				console.error('❌ 生产环境：用户未登录，无法获取用户ID');
				// 生产环境下跳转到登录页面
				window.location.href = '/Login.html';
				return null;
			}
		}

		/**
		 * 获取当前用户信息
		 * @returns {Object|null} 用户信息
		 */
		function getCurrentUser() {
			if (window.authManager && window.authManager.isLoggedIn()) {
				return window.authManager.getCurrentUser();
			}
			return null;
		}

		/**
		 * 检查用户登录状态并显示用户信息
		 */
		function checkUserLoginStatus() {
			const user = getCurrentUser();
			if (user) {
				console.log(`👤 当前用户: ${user.nickname} (ID: ${user.userId})`);
				// 可以在界面上显示用户信息
				updateUserDisplay(user);
			} else {
				console.log('👤 当前使用测试模式');
			}
		}

		/**
		 * 更新界面上的用户显示信息
		 * @param {Object} user 用户信息
		 */
		function updateUserDisplay(user) {
			// 这里可以更新界面上的用户信息显示
			// 例如在页面顶部显示用户昵称等
			try {
				// 如果有用户信息显示区域，可以在这里更新
				const userDisplayElement = document.querySelector('.user-info');
				if (userDisplayElement) {
					userDisplayElement.textContent = `欢迎，${user.nickname}`;
				}
			} catch (error) {
				console.warn('更新用户显示信息失败:', error);
			}
		}

		/**
		 * 处理API调用错误
		 * @param {Error} error 错误对象
		 * @param {string} operation 操作名称
		 * @returns {boolean} 是否需要重新登录
		 */
		function handleApiError(error, operation = '操作') {
			console.error(`${operation}失败:`, error);

			// 检查是否是认证错误
			if (error.status === 401 || error.message?.includes('401') || error.message?.includes('未授权')) {
				console.log('🔐 检测到认证错误，可能需要重新登录');

				if (window.authManager) {
					// 清除本地登录状态
					window.authManager.logout();

					// 提示用户重新登录
					const shouldLogin = confirm('登录状态已过期，是否前往登录页面？');
					if (shouldLogin) {
						window.location.href = '/login.html';
						return true;
					}
				}
			}

			return false;
		}

		/**
		 * 统一的消息显示函数
		 * @param {string} message - 消息内容
		 * @param {string} type - 消息类型：success, error, warning, info
		 */
		function showMessage(message, type = 'info') {
			console.log(`[${type.toUpperCase()}] ${message}`);

			// 如果有父窗口的showBox函数，优先使用
			if (window.parent && window.parent.showBox) {
				window.parent.showBox(message);
				return;
			}

			// 如果有父窗口的Alert函数，使用它
			if (window.parent && window.parent.Alert) {
				window.parent.Alert(message);
				return;
			}

			// 否则使用浏览器原生alert
			alert(message);
		}

		/**
		 * 安全的API调用包装器
		 * @param {Function} apiCall API调用函数
		 * @param {string} operation 操作名称
		 * @returns {Promise} API调用结果
		 */
		async function safeApiCall(apiCall, operation = '操作') {
			try {
				return await apiCall();
			} catch (error) {
				const needRelogin = handleApiError(error, operation);
				if (needRelogin) {
					throw new Error('需要重新登录');
				}
				throw error;
			}
		}

		/**
		 * 切换主战宠物
		 * @param {string|number} petId - 宠物ID
		 */
		async function switchMainPet(petId) {
			console.log(`🔄 切换主战宠物: ${petId}`);

			try {
				// 使用API调用后端接口
				console.log('使用API调用后端接口');
				const userId = getCurrentUserId();
				const response = await fetch('/api/Game/switch-pet', {
					method: 'POST',
					headers: window.petSynthesisApi.getAuthHeaders(),
					body: JSON.stringify({ userId, petId: parseInt(petId) })
				});

				if (!response.ok) {
					if (response.status === 401) {
						handleUnauthorized();
						return false;
					}
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const result = await response.json();
				console.log('✅ 主战宠物切换成功');
				showMessage('主战宠物切换成功', 'success');
				return true;

			} catch (error) {
				console.error('❌ 切换主战宠物异常:', error);
				showMessage('切换主战宠物失败，请重试', 'error');
				return false;
			}
		}

		/**
		 * 处理未授权响应，跳转到登录页面
		 */
		function handleUnauthorized() {
			console.log('🚨 处理未授权响应...');
			showMessage('登录已过期，请重新登录', 'error');
			setTimeout(redirectToLogin, 2000);
		}

		/**
		 * 跳转到登录页面
		 */
		function redirectToLogin() {
			console.log('🔄 跳转到登录页面...');
			// 优先跳转顶层页面，确保完全退出当前应用
			if (window.top && window.top.location) {
				window.top.location.href = '/login.html';
			} else {
				window.location.href = '/login.html';
			}
		}

		$(function(){
			// 页面加载完成后初始化
			console.log('🚀 宠物神殿页面初始化开始...');

			// 等待DOM完全加载后再初始化
			setTimeout(async () => {
				try {
					console.log('📋 开始初始化进化标签页...');

					// 确保进化标签页是激活状态，但不立即加载数据
					console.log('🎯 设置进化标签页为激活状态...');
					setTabDisplay('tab', 1, 3); // 只设置显示，不加载数据

					// 延迟加载进化数据，避免页面加载时的性能问题
					console.log('⏳ 延迟加载进化数据...');
					setTimeout(async () => {
						await loadEvolutionData();
						console.log('✅ 初始化完成：进化数据已加载');
					}, 200);

				} catch (error) {
					console.error('❌ 初始化失败:', error);
					// 如果是未授权错误，处理登录跳转
					if (error.message && error.message.includes('401')) {
						handleUnauthorized();
					}
				}
			}, 100); // 延迟100ms确保DOM完全加载
		});

		/**
		 * 只设置标签页显示，不加载数据（用于初始化）
		 * @param {string} name - 标签页名称前缀
		 * @param {number} cursel - 当前选中的标签页索引
		 * @param {number} n - 标签页总数
		 */
		function setTabDisplay(name, cursel, n) {
			for (let i = 1; i <= n; i++) {
				const menu = document.getElementById(name + i);
				const con = document.getElementById("con_" + name + "_" + i);

				if (menu && con) {
					if (i == cursel) {
						menu.className = "on";
						con.style.display = "block";
					} else {
						menu.className = "";
						con.style.display = "none";
					}
				}
			}
			console.log(`📋 标签页显示已设置：${name}${cursel}`);
		}

		/**
		 * 设置标签页切换功能（包含数据加载）
		 * @param {string} name - 标签页名称前缀
		 * @param {number} cursel - 当前选中的标签页索引
		 * @param {number} n - 标签页总数
		 * @param {boolean} loadData - 是否加载数据，默认true
		 */
		async function setTab(name, cursel, n, loadData = true) {
            // 先设置显示
            setTabDisplay(name, cursel, n);

            // 如果不需要加载数据，直接返回
            if (!loadData) {
                return;
            }

            // 根据选中的标签页加载相应的数据
            if (name === 'tab') {
                try {
                    switch (cursel) {
                        case 1: // 进化标签页
                            console.log('🔄 切换到进化标签页，开始加载数据...');
                            await loadEvolutionData();
                            break;
                        case 2: // 合成标签页
                            console.log('🔄 切换到合成标签页，开始加载数据...');
                            await loadHechengData();
                            break;
                        case 3: // 神宠涅槃标签页
                            console.log('🔄 切换到涅槃标签页，开始加载数据...');
                            await loadNirvanaData();
                            break;
                    }
                } catch (error) {
                    console.error('❌ 标签页数据加载失败:', error);
                    showMessage('数据加载失败，请重试', 'error');
                }
            }
        }

        /**
         * 加载合成页面所需的数据
         */
        async function loadHechengData() {
            try {
                console.log('🔄 开始加载合成数据...');

                // 加载可合成宠物列表
                console.log('正在加载可合成宠物列表...');
                await loadSynthesisPets();
                console.log('合成宠物列表加载完成');

                // 加载合成道具列表
                console.log('正在加载合成道具列表...');
                await loadHechengProp();
                console.log('合成道具列表加载完成');

                console.log('✅ 合成数据加载完成');
            } catch (error) {
                console.error('❌ 加载合成数据失败:', error);
                showMessage('加载合成数据失败，请重试', 'error');
            }
        }

        /**
         * 加载涅槃页面所需的数据
         */
        async function loadNirvanaData() {
            try {
                console.log('🔄 开始加载涅槃数据...');

                // 加载可涅槃的宠物列表
                console.log('正在加载可涅槃宠物列表...');
                await loadNirvanaPets();
                console.log('涅槃宠物列表加载完成');

                // 加载涅槃道具列表
                console.log('正在加载涅槃道具列表...');
                await loadNiepanProp();
                console.log('涅槃道具列表加载完成');

                // 加载涅槃兽列表
                console.log('正在加载涅槃兽列表...');
                await loadNirvanaBeasts();
                console.log('涅槃兽列表加载完成');

                console.log('✅ 涅槃数据加载完成');
            } catch (error) {
                console.error('❌ 加载涅槃数据失败:', error);
                showMessage('加载涅槃数据失败，请重试', 'error');
            }
        }

        /**
         * 绑定宠物点击事件 - 统一的事件处理函数
         */
        function bindPetClickEvents() {
            console.log('🔗 开始绑定宠物点击事件...');

            // 绑定宠物点击事件
            $(".sd_pet").off('click').on('click', function(){
                var petId = $(this).find(".p4").html();
                console.log(`🖱️ 点击宠物: ID=${petId}`);

                // 移除其他宠物的选中状态
                $(".sd_pet").removeClass("selected");
                // 添加当前宠物的选中状态
                $(this).addClass("selected");

                // 1. 切换主宠（如果需要）
                switchMainPet(petId).then(() => {
                    console.log(`🔄 主宠切换完成: ${petId}`);
                }).catch(error => {
                    console.warn(`⚠️ 主宠切换失败: ${error.message}`);
                });

                // 2. 获取进化信息
                getJH(petId, this);
                sxcw = petId;
                $(".进化ID").html(petId);

                console.log(`✅ 选中宠物ID: ${sxcw}`);
            });

            console.log(`🔗 已绑定${$(".sd_pet").length}个宠物的点击事件`);

            // 自动选择第一个可用宠物（正常状态的宠物）
            const firstAvailablePet = $(".sd_pet:not(.yy)").first();
            if (firstAvailablePet.length > 0) {
                console.log('🎯 自动选择第一个可用宠物...');

                // 添加选中状态
                firstAvailablePet.addClass("selected");

                // 获取宠物ID
                const firstPetId = firstAvailablePet.find(".p4").html();
                sxcw = firstPetId;
                $(".进化ID").html(firstPetId);

                console.log(`✅ 自动选中宠物ID: ${sxcw}`);

                // 自动获取进化信息
                getJH(firstPetId, firstAvailablePet[0]);
            } else {
                console.warn('⚠️ 没有找到可用的宠物');
            }
        }

        /**
         * 加载合成页面专用的宠物列表
         */
        async function loadSynthesisPets() {
            try {
                // 调用API获取可合成的宠物列表
                const userId = getCurrentUserId();
                console.log(`🔄 正在调用合成宠物API: /api/Player/synthesis-available?userId=${userId}`);

                const response = await fetch(`/api/Player/synthesis-available?userId=${userId}`, {
                    method: 'GET',
                    headers: window.petSynthesisApi.getAuthHeaders()
                });

                console.log('📡 合成宠物API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    if (response.status === 401) {
                        handleUnauthorized();
                        return;
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('📦 合成宠物API响应:', result);

                if (!result.success && !result.Success) {
                    console.error('❌ 获取合成宠物列表失败:', result.message || result.Message);
                    showMessage('获取合成宠物列表失败：' + (result.message || result.Message), 'error');
                    return;
                }

                let j = result.data || result.Data;
                console.log(`✅ 获取到${j ? j.length : 0}个可合成宠物`);

                if (!j || j.length === 0) {
                    console.warn('⚠️ 可合成宠物列表为空');
                    showMessage('暂无可合成宠物', 'warning');
                    j = [];
                }

                // 更新合成页面的宠物下拉菜单和显示
                updateSynthesisPetDisplay(j);

            } catch (error) {
                console.error('❌ 加载合成宠物列表失败:', error);
                showMessage('加载合成宠物列表失败，请重试', 'error');
            }
        }

        /**
         * 加载涅槃页面专用的宠物列表
         */
        async function loadNirvanaPets() {
            try {
                // 使用专门的涅槃宠物API，只返回神系宠物
                const userId = getCurrentUserId();
                console.log(`🔄 正在调用涅槃宠物API: /api/Player/nirvana-available?userId=${userId}`);

                const response = await fetch(`/api/Player/nirvana-available?userId=${userId}`, {
                    method: 'GET',
                    headers: window.petSynthesisApi.getAuthHeaders()
                });

                console.log('📡 涅槃宠物API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    if (response.status === 401) {
                        handleUnauthorized();
                        return;
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('📦 涅槃宠物API响应:', result);

                if (!result.success && !result.Success) {
                    console.error('❌ 获取涅槃宠物列表失败:', result.message || result.Message);
                    showMessage('获取涅槃宠物列表失败：' + (result.message || result.Message), 'error');
                    return;
                }

                let j = result.data || result.Data;
                console.log(`✅ 获取到${j ? j.length : 0}个可涅槃神系宠物`);

                if (!j || j.length === 0) {
                    console.warn('⚠️ 可涅槃神系宠物列表为空');
                    showMessage('暂无可涅槃神系宠物（需要60级以上的神、神圣、聖、佛、魔、人、鬼、巫、萌、仙、灵、次元系宠物）', 'warning');
                    j = [];
                }

                // 更新涅槃页面的宠物下拉菜单和显示
                updateNirvanaPetDisplay(j);

            } catch (error) {
                console.error('❌ 加载涅槃宠物列表失败:', error);
                showMessage('加载涅槃宠物列表失败，请重试', 'error');
            }
        }

        /**
         * 加载进化页面所需的数据
         */
        async function loadEvolutionData() {
            try {
                console.log('开始加载进化页面数据...');

                // 只加载进化相关的宠物列表
                console.log('正在加载可进化宠物列表...');
                await loadEvolutionPets();
                console.log('进化宠物列表加载完成');

                // 清空进化信息显示
                $(".A进化等级").html("");
                $(".A进化材料").html("");
                $(".A进化名字").html("请选择宠物");
                $(".B进化等级").html("");
                $(".B进化材料").html("");
                $(".B进化名字").html("请选择宠物");
                $(".进化ID").html("");

                // 绑定宠物点击事件 - 确保进化页面的宠物可以点击
                bindPetClickEvents();

                console.log('✅ 进化数据加载完成');
            } catch (error) {
                console.error('❌ 加载进化数据失败:', error);
            }
        }

        /**
         * 加载进化页面专用的宠物列表
         */
        async function loadEvolutionPets() {
            try {
                // 使用专门的进化宠物API，只返回携带的宠物
                const userId = getCurrentUserId();
                console.log(`🔄 正在调用进化宠物API: /api/Player/evolution-available?userId=${userId}`);

                const response = await fetch(`/api/Player/evolution-available?userId=${userId}`, {
                    method: 'GET',
                    headers: window.petSynthesisApi.getAuthHeaders()
                });

                console.log('📡 进化宠物API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    if (response.status === 401) {
                        handleUnauthorized();
                        return;
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('📦 进化宠物API响应:', result);

                if (!result.success && !result.Success) {
                    console.error('❌ 获取进化宠物列表失败:', result.message || result.Message);
                    showMessage('获取进化宠物列表失败：' + (result.message || result.Message), 'error');
                    return;
                }

                let j = result.data || result.Data;
                console.log(`✅ 获取到${j ? j.length : 0}个可进化携带宠物`);

                if (!j || j.length === 0) {
                    console.warn('⚠️ 可进化携带宠物列表为空');
                    showMessage('暂无可进化携带宠物（只能进化携带的宠物）', 'warning');
                    j = [];
                }

                // 只更新进化页面的宠物显示，不影响合成和涅槃页面
                updateEvolutionPetDisplay(j);

            } catch (error) {
                console.error('❌ 加载进化宠物列表失败:', error);
                showMessage('加载进化宠物列表失败，请重试', 'error');
            }
        }

        /**
         * 更新进化页面的宠物显示
         * @param {Array} pets - 宠物数据数组
         */
        function updateEvolutionPetDisplay(pets) {
            try {
                console.log('🎨 更新进化页面宠物显示...');

                // 清空现有显示
                $(".宠物背包").html("");

                // 如果没有宠物，显示提示
                if (!pets || pets.length === 0) {
                    $(".宠物背包").html('<div class="no-pets">暂无可进化宠物</div>');
                    return;
                }

                // 生成宠物显示HTML - 使用传统的sd_pet格式保持一致性
                let petHtml = '';
                for (let i = 0; i < pets.length; i++) {
                    const pet = pets[i];
                    const petImage = pet.形象 || '001';
                    const petStatus = pet.状态 || "0";

                    if (petStatus == "0") {
                        // 正常状态宠物 - 使用统一的点击处理，不需要特殊的onclick
                        petHtml += `<div class="sd_pet r00">
                            <img src="/game/pages/Content/PetPhoto/k${petImage}.gif" onerror="this.src='/game/pages/Content/PetPhoto/k${petImage}.png'"
                                 style="cursor:pointer;opacity: 1;filter : progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=100, finishOpacity=100);"
                                 id="evo_${i+1}" alt="${pet.宠物名字}" />
                            <span style='display:none' class='p4'>${pet.宠物序号}</span>
                        </div>`;
                    } else {
                        // 忙碌状态宠物
                        petHtml += `<div class="sd_pet r00 yy">
                            <img src="/game/pages/Content/PetPhoto/k${petImage}.gif" onerror="this.src='/game/pages/Content/PetPhoto/k${petImage}.png'"
                                 style="cursor:pointer;" id="evo_${i+1}" alt="${pet.宠物名字}" />
                            <span style='display:none' class='p4'>${pet.宠物序号}</span>
                        </div>`;
                    }
                }

                $(".宠物背包").html(petHtml);
                console.log('✅ 进化页面宠物显示更新完成');

                // 注意：不在这里绑定点击事件，统一由loadHechengPet函数中的事件绑定处理

            } catch (error) {
                console.error('❌ 更新进化页面宠物显示失败:', error);
            }
        }

        /**
         * 更新合成页面的宠物显示
         * @param {Array} pets - 宠物数据数组
         */
        function updateSynthesisPetDisplay(pets) {
            try {
                console.log('🎨 更新合成页面宠物显示...');

                // 保存当前选中的值
                const currentMainPet = $("#comapets_a").val();
                const currentSubPet = $("#comapets_b").val();

                // 更新主宠下拉菜单
                $("#comapets_a").html('<option value="-1">请选择主宠</option>');
                // 更新副宠下拉菜单
                $("#comapets_b").html('<option value="-1">请选择副宠</option>');

                if (!pets || pets.length === 0) {
                    console.warn('⚠️ 合成宠物列表为空');
                    return;
                }

                // 存储宠物数据供动态过滤使用
                window.synthesisAvailablePets = pets;

                // 填充下拉菜单选项 - 保持原来的格式以兼容selectHCPET函数
                for (let i = 0; i < pets.length; i++) {
                    const pet = pets[i];
                    const petImage = pet.形象 || '001';
                    const option = `<option value="${pet.宠物序号}-k${petImage}.gif">${pet.宠物名字}-${pet.等级}</option>`;
                    $("#comapets_a").append(option);
                    $("#comapets_b").append(option);
                }

                // 恢复之前选中的值（如果仍然有效）
                if (currentMainPet && currentMainPet !== '-1') {
                    $("#comapets_a").val(currentMainPet);
                }
                if (currentSubPet && currentSubPet !== '-1') {
                    $("#comapets_b").val(currentSubPet);
                }

                console.log('✅ 合成页面宠物显示更新完成');

            } catch (error) {
                console.error('❌ 更新合成页面宠物显示失败:', error);
            }
        }

        /**
         * 更新涅槃页面的宠物显示
         * @param {Array} pets - 宠物数据数组
         */
        function updateNirvanaPetDisplay(pets) {
            try {
                console.log('🎨 更新涅槃页面宠物显示...');

                // 保存当前选中的值
                const currentMainPet = $("#zsapets_a").val();
                const currentSubPet = $("#zsbpets_b").val();

                // 更新主宠下拉菜单
                $("#zsapets_a").html('<option value="-1">请选择主宠</option>');
                // 更新副宠下拉菜单
                $("#zsbpets_b").html('<option value="-1">请选择副宠</option>');

                if (!pets || pets.length === 0) {
                    console.warn('⚠️ 涅槃宠物列表为空');
                    return;
                }

                // 存储宠物数据供动态过滤使用
                window.nirvanaAvailablePets = pets;

                // 填充下拉菜单选项 - 保持原来的格式以兼容涅槃函数
                for (let i = 0; i < pets.length; i++) {
                    const pet = pets[i];
                    const petImage = pet.形象 || '001';
                    const option = `<option value="${pet.宠物序号}-k${petImage}.gif">${pet.宠物名字}-${pet.等级}</option>`;
                    $("#zsapets_a").append(option);
                    $("#zsbpets_b").append(option);
                }

                // 恢复之前选中的值（如果仍然有效）
                if (currentMainPet && currentMainPet !== '-1') {
                    $("#zsapets_a").val(currentMainPet);
                }
                if (currentSubPet && currentSubPet !== '-1') {
                    $("#zsbpets_b").val(currentSubPet);
                }

                console.log('✅ 涅槃页面宠物显示更新完成');

            } catch (error) {
                console.error('❌ 更新涅槃页面宠物显示失败:', error);
            }
        }

        /**
         * 动态更新合成下拉菜单选项，排除已选择的宠物
         */
        function updateSynthesisDropdownOptions() {
            try {
                if (!window.synthesisAvailablePets) {
                    console.log('⚠️ 合成宠物数据未加载，跳过动态过滤');
                    return; // 如果没有宠物数据，直接返回
                }

                const currentMainPet = $("#comapets_a").val();
                const currentSubPet = $("#comapets_b").val();

                console.log(`🔄 动态过滤合成下拉菜单: 主宠=${currentMainPet}, 副宠=${currentSubPet}`);

                // 获取主宠和副宠的ID
                const mainPetId = currentMainPet && currentMainPet !== '-1' ? currentMainPet.split("-")[0] : null;
                const subPetId = currentSubPet && currentSubPet !== '-1' ? currentSubPet.split("-")[0] : null;

                // 只有当选择了宠物时才进行过滤
                if (!mainPetId && !subPetId) {
                    console.log('📝 未选择任何宠物，跳过过滤');
                    return;
                }

                // 重新填充主宠下拉菜单（排除副宠）
                const mainPetSelect = $("#comapets_a");
                mainPetSelect.html('<option value="-1">请选择主宠</option>');

                window.synthesisAvailablePets.forEach(pet => {
                    const petValue = `${pet.宠物序号}-k${pet.形象 || '001'}.gif`;
                    const petId = pet.宠物序号.toString();

                    // 如果这只宠物不是当前选中的副宠，则添加到主宠选项中
                    if (petId !== subPetId) {
                        const option = `<option value="${petValue}">${pet.宠物名字}-${pet.等级}</option>`;
                        mainPetSelect.append(option);
                    }
                });

                // 恢复主宠选择
                if (mainPetId) {
                    mainPetSelect.val(currentMainPet);
                }

                // 重新填充副宠下拉菜单（排除主宠）
                const subPetSelect = $("#comapets_b");
                subPetSelect.html('<option value="-1">请选择副宠</option>');

                window.synthesisAvailablePets.forEach(pet => {
                    const petValue = `${pet.宠物序号}-k${pet.形象 || '001'}.gif`;
                    const petId = pet.宠物序号.toString();

                    // 如果这只宠物不是当前选中的主宠，则添加到副宠选项中
                    if (petId !== mainPetId) {
                        const option = `<option value="${petValue}">${pet.宠物名字}-${pet.等级}</option>`;
                        subPetSelect.append(option);
                    }
                });

                // 恢复副宠选择
                if (subPetId) {
                    subPetSelect.val(currentSubPet);
                }

                console.log('✅ 合成下拉菜单动态过滤完成');

            } catch (error) {
                console.error('❌ 更新合成下拉菜单选项失败:', error);
            }
        }

        /**
         * 动态更新涅槃下拉菜单选项，排除已选择的宠物
         */
        function updateNirvanaDropdownOptions() {
            try {
                if (!window.nirvanaAvailablePets) {
                    console.log('⚠️ 涅槃宠物数据未加载，跳过动态过滤');
                    return; // 如果没有宠物数据，直接返回
                }

                const currentMainPet = $("#zsapets_a").val();
                const currentSubPet = $("#zsbpets_b").val();

                console.log(`🔄 动态过滤涅槃下拉菜单: 主宠=${currentMainPet}, 副宠=${currentSubPet}`);

                // 获取主宠和副宠的ID
                const mainPetId = currentMainPet && currentMainPet !== '-1' ? currentMainPet.split("-")[0] : null;
                const subPetId = currentSubPet && currentSubPet !== '-1' ? currentSubPet.split("-")[0] : null;

                // 只有当选择了宠物时才进行过滤
                if (!mainPetId && !subPetId) {
                    console.log('📝 未选择任何宠物，跳过过滤');
                    return;
                }

                // 重新填充主宠下拉菜单（排除副宠）
                const mainPetSelect = $("#zsapets_a");
                mainPetSelect.html('<option value="-1">请选择主宠</option>');

                window.nirvanaAvailablePets.forEach(pet => {
                    const petValue = `${pet.宠物序号}-k${pet.形象 || '001'}.gif`;
                    const petId = pet.宠物序号.toString();

                    // 如果这只宠物不是当前选中的副宠，则添加到主宠选项中
                    if (petId !== subPetId) {
                        const option = `<option value="${petValue}">${pet.宠物名字}-${pet.等级}</option>`;
                        mainPetSelect.append(option);
                    }
                });

                // 恢复主宠选择
                if (mainPetId) {
                    mainPetSelect.val(currentMainPet);
                }

                // 重新填充副宠下拉菜单（排除主宠）
                const subPetSelect = $("#zsbpets_b");
                subPetSelect.html('<option value="-1">请选择副宠</option>');

                window.nirvanaAvailablePets.forEach(pet => {
                    const petValue = `${pet.宠物序号}-k${pet.形象 || '001'}.gif`;
                    const petId = pet.宠物序号.toString();

                    // 如果这只宠物不是当前选中的主宠，则添加到副宠选项中
                    if (petId !== mainPetId) {
                        const option = `<option value="${petValue}">${pet.宠物名字}-${pet.等级}</option>`;
                        subPetSelect.append(option);
                    }
                });

                // 恢复副宠选择
                if (subPetId) {
                    subPetSelect.val(currentSubPet);
                }

                console.log('✅ 涅槃下拉菜单动态过滤完成');

            } catch (error) {
                console.error('❌ 更新涅槃下拉菜单选项失败:', error);
            }
        }

		/**
		 * 加载合成道具列表到下拉菜单
		 */
		async function loadHechengProp() {
			try {
				console.log('🔄 开始加载合成道具列表...');

				// 使用专门的合成材料API
				const userId = getCurrentUserId();
				console.log(`🔄 正在调用合成材料API: /api/Prop/synthesis-materials?userId=${userId}`);

				const response = await fetch(`/api/Prop/synthesis-materials?userId=${userId}`, {
					method: 'GET',
					headers: window.petSynthesisApi.getAuthHeaders()
				});

				if (!response.ok) {
					if (response.status === 401) {
						handleUnauthorized();
						return;
					}
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const result = await response.json();
				console.log('📦 合成材料API响应:', result);

				if (!result.success) {
					console.error('❌ 获取合成材料失败:', result.message);
					showMessage('获取合成材料失败：' + result.message, 'error');
					return;
				}

				const synthesisItems = result.data || [];
				console.log(`✅ 获取到${synthesisItems.length}个合成材料`);

				var value = $("#wp1").val();
				var value1 = $("#wp2").val();
				$(".合成道具列表").html("<option value='-1'>请选择材料</option>");
				for(var i = 0; i < synthesisItems.length; i++){
					const item = synthesisItems[i];
					// 使用新API返回的字段名
					const itemId = item.道具序号 || item.道具类型ID;
					const itemName = item.道具名字;
					const itemCount = item.道具数量 || item.数量 || 1;
					$(".合成道具列表").append(`<option value="${itemId}">${itemName} (${itemCount}个)</option>`);
				}
				$("#wp1").val(value).change();
				$("#wp2").val(value1).change();

				console.log('✅ 合成道具列表加载完成');
			} catch (error) {
				console.error('❌ 加载合成道具失败:', error);
				showMessage('加载合成道具失败，请重试', 'error');
			}
		}
		/**
		 * 加载可合成宠物列表到下拉菜单和宠物背包显示
		 * @deprecated 此函数已废弃，请使用专门的 loadSynthesisPets, loadEvolutionPets, loadNirvanaPets
		 */
		async function loadHechengPet() {
			console.warn('⚠️ loadHechengPet 函数已废弃，建议使用专门的加载函数');
			// 为了兼容性，默认加载合成宠物数据
			await loadSynthesisPets();
		}

		/**
		 * 加载涅槃道具列表到下拉菜单
		 * @param {string} json - 包含涅槃道具信息的JSON字符串 (可选，用于兼容)
		 */
		async function loadNiepanProp(json = null) {
			try {	
                console.log(`✅ 获取到${j ? j.length : 0}个可合成宠物`);

				if (!j || j.length === 0) {
					console.warn('⚠️ 宠物列表为空');
					showMessage('暂无可用宠物', 'warning');
					j = [];
				}

				$("#cp1").hide();
				$("#cp2").hide();
				$("#zscp1").hide();
				$("#zscp2").hide();
				var value =  $("#comapets_a").val();

				var value1 =  $("#comapets_b").val();
				var value2 =  $("#zsapets_a").val();
				var value3 =  $("#zsbpets_b").val();
				var value4 =  $("#zs").val();
				console.log(`🎒 开始生成宠物背包，共${j.length}个宠物`);
				$(".宠物背包").html("");
				 $(".合成宠物列表").html("<option value=''>请选择宠物</option>");
				var ii = 0;
				var normalPetCount = 0;
				var busyPetCount = 0;

				for(var i = 0;i<j.length;i++){
					ii++;

					// 检查宠物状态，如果没有状态字段则默认为正常状态
					var petStatus = j[i].状态 || "0";
					var petName = j[i].宠物名字 || "";
					var petLevel = j[i].等级 || 1;
					var petImage = j[i].形象 || "001";
					var petId = j[i].宠物序号 || i;
					var petElement = j[i].五行 || "";

					console.log(`🐾 处理宠物${i+1}: ID=${petId}, 名称=${petName}, 等级=${petLevel}, 状态=${petStatus}, 形象=${petImage}`);

					if(petStatus == "0"){
						normalPetCount++;
						var petHtml = "<div class=\"sd_pet r00\"><img  src=\"/game/pages/Content/PetPhoto/k"+petImage+".gif\" onerror=\"this.src='/game/pages/Content/PetPhoto/k"+petImage+".png'\" style=\"cursor:pointer;opacity: 1;filter : progid:DXImageTransform.Microsoft.Alpha(style=0, opacity=100, finishOpacity=100);\" id=\"i"+(i+1)+"\"/><span style='display:none' class='p4'>"+petId+"</span></div>";
						$(".宠物背包").html($(".宠物背包").html() + petHtml);
						$(".进化ID").html(petId);
						console.log(`✅ 添加正常状态宠物: ${petName}`);
					}else{
						busyPetCount++;
						var petHtml = "<div class=\"sd_pet r00 yy\"><img src=\"/game/pages/Content/PetPhoto/k"+petImage+".gif\" onerror=\"this.src='/game/pages/Content/PetPhoto/k"+petImage+".png'\" style=\"cursor:pointer;\" id=\"i"+(i+1)+"\"/><span style='display:none' class='p4'>"+petId+"</span></div>";
						$(".宠物背包").html($(".宠物背包").html() + petHtml);
						console.log(`⚠️ 添加忙碌状态宠物: ${petName}`);
					}

					// 只有五系宠物才能用于合成
					if(petElement!='神' && petElement!='神圣'  && petElement!='聖' && petElement!='人' && petElement!='鬼' && petElement!='巫' && petElement!='仙'&& petElement!='萌'&& petElement!='佛'&& petElement!='灵'&& petElement!='次元'){
						if(petName!='涅槃兽' && petName.indexOf("涅槃重生")==-1){
							 $(".合成宠物列表:eq(0)").append("<option  value='"+petId+"-k"+petImage+".gif'>"+petName+"-"+petLevel+"</option>");
							 $(".合成宠物列表:eq(1)").append("<option  value='"+petId+"-k"+petImage+".gif'>"+petName+"-"+petLevel+"</option>");
						 }
					 }

					 if(j[i].五行=='神' || j[i].五行=='神圣' || j[i].五行=='聖'|| j[i].五行=='人'|| j[i].五行=='鬼'|| j[i].五行=='巫' || j[i].五行=='仙'|| j[i].五行=='萌'|| j[i].五行=='佛'|| j[i].五行=='灵'|| j[i].五行=='次元'){
						if(j[i].宠物名字!='涅槃兽' && j[i].宠物名字.indexOf("涅槃重生")==-1){

							 $(".合成宠物列表:eq(2)").append("<option  value='"+j[i].宠物序号+"-k"+j[i].形象+".gif'>"+j[i].宠物名字+"-"+j[i].等级+"</option>");
							 $(".合成宠物列表:eq(3)").append("<option  value='"+j[i].宠物序号+"-k"+j[i].形象+".gif'>"+j[i].宠物名字+"-"+j[i].等级+"</option>");
						 }
					 }
					 if(j[i].宠物名字=='涅槃兽' || j[i].宠物名字.indexOf("涅槃重生")!=-1){
						  $(".合成宠物列表:eq(4)").append("<option  value='"+j[i].宠物序号+"-k"+j[i].形象+".gif'>"+j[i].宠物名字+"-"+j[i].等级+"</option>");
					}

				}

				console.log(`📊 宠物背包生成完成: 正常状态${normalPetCount}个, 忙碌状态${busyPetCount}个`);
				console.log(`🎯 当前宠物背包HTML长度: ${$(".宠物背包").html().length}`);

				// 使用统一的点击事件绑定函数
				bindPetClickEvents();

				$("#comapets_a").val(value).change();
				$("#comapets_b").val(value1).change();
				$("#zsapets_a").val(value2).change();
				$("#zsbpets_b").val(value3).change();
			    $("#zs").val(value4).change();

			    console.log(`✅ loadHechengPet 函数执行完成`);
			} catch (error) {
				console.error('❌ 加载宠物列表失败:', error);
			}
		}
        
		/**
		 * 加载涅槃道具列表到下拉菜单
		 * 使用专门的涅槃道具API，基于老系统的图标分类逻辑
		 */
		async function loadNiepanProp() {
			try {
				console.log('🔄 开始加载涅槃道具列表...');

				const userId = getCurrentUserId();
				console.log(`🔄 正在调用涅槃道具API: /api/Prop/nirvana-materials?userId=${userId}`);

				const response = await fetch(`/api/Prop/nirvana-materials?userId=${userId}`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Accept': 'application/json'
					}
				});

				if (!response.ok) {
					if (response.status === 401) {
						const needRelogin = handleApiError({ status: 401 }, '获取涅槃道具');
						if (needRelogin) return;
					}
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const result = await response.json();
				console.log('📦 涅槃道具API响应:', result);

				if (!result.success) {
					console.error('❌ 获取涅槃道具失败:', result.message);
					showMessage('获取涅槃道具失败：' + result.message, 'error');
					return;
				}

				const nirvanaItems = result.data || [];
				console.log(`✅ 获取到${nirvanaItems.length}个涅槃道具`);

				// 按道具类型分组显示
				const nirvanaBeasts = nirvanaItems.filter(item => item.道具类型 === '涅槃兽');
				const nirvanaMaterials = nirvanaItems.filter(item => item.道具类型 === '涅槃材料');

				console.log(`📊 涅槃兽: ${nirvanaBeasts.length}个, 涅槃材料: ${nirvanaMaterials.length}个`);

				// 保存当前选中的值
				var value2 = $("#zswp1").val();
				var value3 = $("#zswp2").val();

				// 清空并重新填充下拉菜单
				$(".合成道具列表1").html("<option value='-1'>请选择材料</option>");

				// 添加涅槃兽分组
				if (nirvanaBeasts.length > 0) {
					$(".合成道具列表1").append('<optgroup label="🐉 涅槃兽类">');
					nirvanaBeasts.forEach(item => {
						$(".合成道具列表1").append(
							`<option value="${item.道具序号}" data-type="涅槃兽" data-icon="${item.道具图标}">` +
							`${item.道具名字} (${item.道具数量}个)</option>`
						);
					});
					$(".合成道具列表1").append('</optgroup>');
				}

				// 添加涅槃材料分组
				if (nirvanaMaterials.length > 0) {
					$(".合成道具列表1").append('<optgroup label="💎 涅槃材料">');
					nirvanaMaterials.forEach(item => {
						$(".合成道具列表1").append(
							`<option value="${item.道具序号}" data-type="涅槃材料" data-icon="${item.道具图标}">` +
							`${item.道具名字} (${item.道具数量}个)</option>`
						);
					});
					$(".合成道具列表1").append('</optgroup>');
				}

				// 如果没有涅槃道具，显示提示
				if (nirvanaItems.length === 0) {
					$(".合成道具列表1").append('<option value="-1" disabled>暂无涅槃道具</option>');
					console.log('ℹ️ 用户背包中没有涅槃道具');
				}

				// 恢复之前选中的值
				$("#zswp1").val(value2).change();
				$("#zswp2").val(value3).change();

				console.log('✅ 涅槃道具列表加载完成');
			} catch (error) {
				console.error('❌ 加载涅槃道具失败:', error);
				showMessage('加载涅槃道具失败，请重试', 'error');

				// 错误时显示基本选项
				$(".合成道具列表1").html("<option value='-1'>加载失败，请重试</option>");
			}
		}

		/**
		 * 加载涅槃兽列表到下拉菜单
		 */
		async function loadNirvanaBeasts() {
			try {
				console.log('🔄 开始加载涅槃兽列表...');

				const userId = getCurrentUserId();
				console.log(`🔄 正在调用涅槃兽API: /api/Nirvana/nirvana-beasts/${userId}`);

				const response = await fetch(`/api/Nirvana/nirvana-beasts/${userId}`, {
					method: 'GET',
					headers: window.petSynthesisApi.getAuthHeaders()
				});

				if (!response.ok) {
					if (response.status === 401) {
						handleUnauthorized();
						return;
					}
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const result = await response.json();
				console.log('🐉 涅槃兽API响应:', result);

				if (!result.success) {
					console.error('❌ 获取涅槃兽失败:', result.message);
					showMessage('获取涅槃兽失败：' + result.message, 'error');
					return;
				}

				const nirvanaBeasts = result.data || [];
				console.log(`✅ 获取到${nirvanaBeasts.length}只涅槃兽`);

				// 保存当前选中的值
				var currentValue = $("#zs").val();

				// 清空并重新填充涅槃兽下拉菜单
				$("#zs").html("<option value=''>请选择涅槃兽</option>");

				// 添加涅槃兽选项
				for(var i = 0; i < nirvanaBeasts.length; i++){
					const beast = nirvanaBeasts[i];
					const beastId = beast.宠物序号;
					const beastName = beast.宠物名字;
					const beastLevel = beast.等级;
					const beastImage = beast.形象 || '103'; // 涅槃兽形象固定为103

					$("#zs").append(`<option value="${beastId}-k${beastImage}.gif">${beastName}-${beastLevel}</option>`);
				}

				// 恢复之前选中的值
				$("#zs").val(currentValue).change();

				console.log('✅ 涅槃兽列表加载完成');
			} catch (error) {
				console.error('❌ 加载涅槃兽失败:', error);
				showMessage('加载涅槃兽失败，请重试', 'error');

				// 错误时显示基本选项
				$("#zs").html("<option value=''>加载失败，请重试</option>");
			}
		}

		var APet=-1;
		var BPet=-1;

		/**
		 * 选择合成宠物并显示预览图片
		 * 同时获取合成配置信息
		 */
		async function selectHCPET(){
			var A = $("#comapets_a").val();
			var B = $("#comapets_b").val();

			if(A!=''&&A!='-1'){
				$("#cp1").show();
				APet = A.split("-")[0];

				$("#cp1").attr("src","/game/pages/Content/PetPhoto/"+A.split("-")[1]);


			}else{
				APet = -1;
				$("#cp1").hide();
			}

			if(B!=''&&B!='-1'){
				BPet = B.split("-")[0];
				$("#cp2").show();
				$("#cp2").attr("src","/game/pages/Content/PetPhoto/"+B.split("-")[1]);

			}else{
				BPet = -1;
				$("#cp2").hide();
			}

			// 如果选择了主宠和副宠，获取合成配置
			if (APet !== -1 && BPet !== -1) {
				try {
					const config = await window.petSynthesisApi.getSynthesisConfig(APet, BPet);
					if (config.success && config.data) {
						// 更新合成信息显示
						updateSynthesisInfo(config.data);
					}
				} catch (error) {
					console.error('获取合成配置失败:', error);
				}
			}

			// 在选择完成后，动态过滤下拉菜单选项，防止选择同一只宠物
			setTimeout(() => {
				updateSynthesisDropdownOptions();
			}, 100);
		}

		/**
		 * 更新合成信息显示
		 * @param {Object} config - 合成配置信息对象
		 */
		function updateSynthesisInfo(config) {
			// 更新成功率显示
			if (config.actualSuccessRate !== undefined) {
				$("#success-rate").text(config.actualSuccessRate.toFixed(1) + '%');
			}

			// 更新神宠概率显示
			if (config.godPetProbability !== undefined) {
				$("#god-pet-rate").text(config.godPetProbability.toFixed(1) + '%');
			}

			// 更新预期成长范围
			if (config.expectedGrowthRange) {
				$("#growth-range").text(config.expectedGrowthRange);
			}
		}
		var CPet=-1;
		var DPet=-1;
		var EPet=-1;

		/**
		 * 选择涅槃宠物并显示预览图片，更新转生预览信息
		 */
		async function selectNPPET(){
			var A = $("#zsapets_a").val();
			var B = $("#zsbpets_b").val();

			console.log(`🔄 转生宠物选择: A=${A}, B=${B}`);

			var C = $("#zs").val();
			if(A!='' && A.includes('-')&&A!='-1'){
				const parts = A.split("-");
				CPet = parts[0];
				const imageFile = parts[1];

				if(imageFile) {
					console.log(`🖼️ 设置主宠图片: ${imageFile}`);
					$("#zsp1").html(`<img src="/game/pages/Content/PetPhoto/${imageFile}" onerror="this.src='/game/pages/Content/PetPhoto/${imageFile.split('.')[0]}.png'" />`);
				} else {
					console.warn(`⚠️ 主宠图片文件名为空: ${A}`);
					$("#zsp1").html(`<img src="/game/pages/Content/PetPhoto/k001.gif" onerror="this.src='/game/pages/Content/PetPhoto/k001.png'" />`);
				}
			}else{
				CPet = -1;
				$("#zscp1").hide();
			}


			if(B!='' && B.includes('-')&&B!='-1'){
				const parts = B.split("-");
				DPet = parts[0];
				const imageFile = parts[1];
				$("#zsp2").show();

				if(imageFile) {
					console.log(`🖼️ 设置副宠图片: ${imageFile}`);
					$("#zsp2").html(`<img src="/game/pages/Content/PetPhoto/${imageFile}" onerror="this.src='/game/pages/Content/PetPhoto/${imageFile.split('.')[0]}.png'" />`);
				} else {
					console.warn(`⚠️ 副宠图片文件名为空: ${B}`);
					$("#zsp2").html(`<img src="/game/pages/Content/PetPhoto/k001.gif" onerror="this.src='/game/pages/Content/PetPhoto/k001.png'" />`);
				}

			}else{
				DPet = -1;
				$("#zscp2").hide();
			}

			if(C!=''){
				EPet = C.split("-")[0];

			}else{
				EPet = -1;

			}

			// 更新转生预览信息
			await updateNirvanaPreview();

			// 在选择完成后，动态过滤下拉菜单选项，防止选择同一只宠物
			setTimeout(() => {
				updateNirvanaDropdownOptions();
			}, 100);

		}

		/**
		 * 更新转生预览信息
		 * 显示预期的成功率、消耗金币等信息
		 */
		async function updateNirvanaPreview() {
			try {
				if (CPet == -1 || DPet == -1 || EPet == -1) {
					return; // 宠物未全部选择
				}

				const userId = getCurrentUserId();
				const zsprop1 = $("#zswp1 option:selected").val();
				const zsprop2 = $("#zswp2 option:selected").val();

				const previewData = {
					UserId: userId,
					MainPetId: parseInt(CPet),
					SubPetId: parseInt(DPet),
					NirvanaPetId: parseInt(EPet),
					UsedItemId1: zsprop1 && zsprop1 !== '-1' ? zsprop1 : null,
					UsedItemId2: zsprop2 && zsprop2 !== '-1' ? zsprop2 : null
				};

				console.log('🔄 发送转生预览请求:', previewData);

				const response = await fetch('/api/Nirvana/preview', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(previewData)
				});

				const result = await response.json();

				if (result.success && result.data) {
					const preview = result.data;
					let previewText = `转生预览：成功率 ${(preview.estimatedSuccessRate * 100).toFixed(1)}%，消耗 ${preview.estimatedCostGold?.toLocaleString() || '500,000'} 金币`;
					if (preview.vipBonus > 0) {
						previewText += `，VIP加成 ${(preview.vipBonus * 100).toFixed(1)}%`;
					}

					// 更新金币显示区域
					const goldElement = document.querySelector('.sd_hc_r');
					if (goldElement) {
						const existingPreview = goldElement.querySelector('.nirvana-preview');
						if (existingPreview) {
							existingPreview.textContent = previewText;
						} else {
							const previewDiv = document.createElement('div');
							previewDiv.className = 'nirvana-preview';
							previewDiv.style.cssText = 'color: #653E17; font-size: 12px; margin-top: 5px; padding: 5px; background: rgba(255,255,255,0.3); border-radius: 3px;';
							previewDiv.textContent = previewText;
							goldElement.appendChild(previewDiv);
						}
					}
				}
			} catch (error) {
				console.error('获取转生预览失败:', error);
			}
		}
		/**
		 * 获取宠物进化信息并设置选中状态
		 * @param {number} i - 宠物ID
		 * @param {Object} t - 点击的DOM元素
		 */
		async function getJH(i,t){
			$(".sd_pet").addClass("yy");

			$(".进化ID").html(i);

			try {
				// 调用新的API获取宠物进化信息
				const headers = {
					'Content-Type': 'application/json',
					'X-User-Id': '1' // 临时使用固定用户ID
				};

				const response = await fetch(`/api/PetEvolution/${i}/info`, {
					method: 'GET',
					headers: headers
				});

				// 检查HTTP状态码
				if (response.status === 401) {
					console.warn('用户未登录，跳转到登录页面');
					handleUnauthorized();
					return;
				}

				const result = await response.json();
				console.log('🔍 API响应详情:', result);
				console.log('🔍 Success字段:', result.Success, result.success);
				console.log('🔍 ErrorCode字段:', result.ErrorCode, result.errorCode);

				// 检查是否是未授权错误 (兼容大小写)
				if ((!result.Success || !result.success) &&
					(result.ErrorCode === 'UNAUTHORIZED' || result.errorCode === 'UNAUTHORIZED')) {
					console.warn('🚨 检测到未授权错误，跳转到登录页面');
					handleUnauthorized();
					return;
				}

				if ((result.Success || result.success) && (result.Data || result.data)) {
					// 转换API返回的数据格式为原有格式 (兼容大小写)
					const data = result.Data || result.data;
					console.log('🔍 处理进化数据:', data);

					const evolutionData = {
						AI: -1, APN: "", ALV: 0, AN: "",
						BI: -1, BPN: "", BLV: 0, BN: ""
					};

					// 处理进化配置 (兼容大小写字段名)
					const evolutions = data.AvailableEvolutions || data.availableEvolutions || [];
					console.log('🔍 可用进化配置:', evolutions);

					if (evolutions && evolutions.length > 0) {
						// 处理A路线进化配置
						const aConfig = evolutions.find(e =>
							(e.EvolutionType === 'A' || e.evolutionType === 'A'));
						console.log('🔍 A路线配置:', aConfig);

						if (aConfig) {
							evolutionData.AI = aConfig.TargetPetNo || aConfig.targetPetNo || data.PetNo || data.petNo;
                            evolutionData.APN = aConfig.itemName || aConfig.itemName || "";
							evolutionData.ALV = aConfig.RequiredLevel || aConfig.requiredLevel || 0;

							const canEvolveA = aConfig.CanEvolve !== undefined ? aConfig.CanEvolve : aConfig.canEvolve;
							const reasonA = aConfig.CannotEvolveReason || aConfig.cannotEvolveReason || "不可进化";
							const growthMinA = aConfig.GrowthMin || aConfig.growthMin || 0;
							const growthMaxA = aConfig.GrowthMax || aConfig.growthMax || 0;

							evolutionData.AN = canEvolveA ?
								`A型进化 (成长+${growthMinA}-${growthMaxA})` :
								reasonA;

							console.log('✅ A路线处理结果:', {
								canEvolve: canEvolveA,
								reason: reasonA,
								display: evolutionData.AN
							});
						}

						// 处理B路线进化配置
						const bConfig = evolutions.find(e =>
							(e.EvolutionType === 'B' || e.evolutionType === 'B'));
						console.log('🔍 B路线配置:', bConfig);

						if (bConfig) {
							evolutionData.BI = bConfig.TargetPetNo || bConfig.targetPetNo || data.PetNo || data.petNo;
                            evolutionData.BPN = bConfig.itemName || bConfig.itemName || "";
							evolutionData.BLV = bConfig.RequiredLevel || bConfig.requiredLevel || 0;

							const canEvolveB = bConfig.CanEvolve !== undefined ? bConfig.CanEvolve : bConfig.canEvolve;
							const reasonB = bConfig.CannotEvolveReason || bConfig.cannotEvolveReason || "不可进化";
							const growthMinB = bConfig.GrowthMin || bConfig.growthMin || 0;
							const growthMaxB = bConfig.GrowthMax || bConfig.growthMax || 0;

							evolutionData.BN = canEvolveB ?
								`B型进化 (成长+${growthMinB}-${growthMaxB})` :
								reasonB;

							console.log('✅ B路线处理结果:', {
								canEvolve: canEvolveB,
								reason: reasonB,
								display: evolutionData.BN
							});
						}
					}

					readJH(JSON.stringify(evolutionData));
				} else {
					// 如果API调用失败，显示默认的不可进化信息
					const defaultData = {
						AI: -1, APN: "", ALV: 0, AN: "不可进化",
						BI: -1, BPN: "", BLV: 0, BN: "不可进化"
					};
					readJH(JSON.stringify(defaultData));
				}
			} catch (error) {
				console.error('获取进化信息失败:', error);
				// 出错时显示默认信息
				const defaultData = {
					AI: -1, APN: "", ALV: 0, AN: "获取信息失败",
					BI: -1, BPN: "", BLV: 0, BN: "获取信息失败"
				};
				readJH(JSON.stringify(defaultData));
			}

			$(t).removeClass("yy");
		}
		
		
		
		
		/**
		 * 读取并显示宠物进化信息
		 * @param {string} json - 包含进化信息的JSON字符串
		 */
		function readJH(json){
			console.log('🔍 readJH 接收到的数据:', json);
			var j = $.parseJSON(json);
			console.log('🔍 解析后的进化数据:', j);

			// A路线进化信息显示
			if(j.AI && j.AI !== -1){
				$(".A进化材料").html(j.APN || "");
				$(".A进化等级").html(j.ALV || "");
				$(".A进化名字").html(j.AN || "不可进化");
				console.log('✅ A路线信息已更新:', {
					材料: j.APN,
					等级: j.ALV,
					名字: j.AN
				});
			}else{
				$(".A进化材料").html("");
				$(".A进化等级").html("");
				$(".A进化名字").html("不可进化");
				console.log('⚠️ A路线设置为不可进化');
			}

			// B路线进化信息显示
			if(j.BI && j.BI !== -1){
				$(".B进化材料").html(j.BPN || "");
				$(".B进化等级").html(j.BLV || "");
				$(".B进化名字").html(j.BN || "不可进化");
				console.log('✅ B路线信息已更新:', {
					材料: j.BPN,
					等级: j.BLV,
					名字: j.BN
				});
			}else{
				$(".B进化材料").html("");
				$(".B进化等级").html("");
				$(".B进化名字").html("不可进化");
				console.log('⚠️ B路线设置为不可进化');
			}

			console.log('🎯 进化信息显示完成');
		}
		

</script>


</head>
<body>
<div id="Layer1" style="cursor:pointer" onclick="showMessage('此功能暂未开放', 'info')">
  <label></label>
</div>
<div class="task">
  <div class="task_left"></div>
  <div class="task_right">
    <a style="POSITION: ABSOLUTE;LEFT: 425PX;TOP: 13PX;COLOR: #ad5d07;font-weight: bold;" href="#" onclick="showMessage('宠物公式功能暂未开放', 'info')">查看宠物公式</a>
    <ul class="task_nav">
      <li id="tab1" onclick="setTab('tab',1,3);" class="on"><a class="a01" href="javascript:void(0)"></a></li>
      <li id="tab2" onclick="setTab('tab',2,3);" class=""><a class="a02" href="javascript:void(0)"></a></li>
      <li id="tab3" onclick="setTab('tab',3,3);" class=""><a class="a03" href="javascript:void(0)"></a></li>
    </ul>
    <div class="dt_task" id="con_tab_1" style="display: block;">
      <div class="sd_l">
        <p> 说明：进化需要满足条件，进化材料可以通过怪物掉落、开启进化箱、进化宝石兑换获得。 </p>
        <div class="sd_item 宠物背包">
          
          @@
          <br>
         
        
        </div>
   
      </div>
      <div class="sd_r">
      	<div class="sd_r">
            <div class="sd_step">
            <p>
                进化需求等级： <span class="A进化等级"></span><br>
                进化所需金币： 1000<br>
                进化所需材料： <span class="A进化材料"></span><br>
                      进化后宠物：  <span class="A进化名字"></span><br>
                        <a href="#"><img src="Content/petMain/img/sd04.jpg" alt="进化" border="0" onclick="JinHua('1')"></a>
                    </p>
                    </div>
                    <div class="sd_step">
                        <p>
                进化需求等级： <span class="B进化等级"></span> <br>
                进化所需金币： 1000<br>
                进化所需材料： <span class="B进化材料"></span><br>
                进化后宠物： <span class="B进化名字"></span><br>
                <a href="#"><img src="Content/petMain/img/sd05.jpg" alt="进化" border="0" onclick="JinHua('2')"></a>
                </p><div class="进化ID" style="display:none"></div>
                <p></p>
            </div>
            </div>
      </div>
    </div>
    <div class="dt_task con" id="con_tab_2" style="display: none;">
      <div class="sd_hc_l">
        <div class="sd_hc_01">
          <div class="hc_pet_01" id="comp1"><img style="display:none" id="cp1"></div>
          <div class="hc_pet_02" id="comp2"><img style="display:none" id="cp2"></div>
        </div>
        <div class="sd_hc_02" id="hcxx">
          <input type="hidden" value="" id="hidd_bbal">
          <table width="260" border="0" cellspacing="0" cellpadding="0">
            <tbody>
              <tr>
                <td align="center"><select class="合成宠物列表" name="comapets" id="comapets_a" style="margin-top:3px;" onchange="selectHCPET();">
                    <option value="">请选择主宠物</option>
                  </select></td>
                <td align="center"><select class="合成宠物列表" name="comapets" id="comapets_b" style="margin-top:3px;" onchange="selectHCPET();">
                    <option value="">请选择副宠物</option>
                  </select></td>
              </tr>
              <tr>
              </tr><tr>
                <td height="30" colspan="2" align="center">合成宠物需要金币：<span id="smoney">50000</span></td>
              </tr>
              
              
            </tbody>
          </table>
        </div>
      </div>
      <div class="sd_hc_r"> <b>合成等级限制：</b>主副宠物均需要40级<br>
        <b>说明：</b><br>
        1）添加的道具可以通过神秘商店、副本等获得；<br>
        2）合成时请先取下宠物装备，否则宠物消失时装备也会一起消失；<br>
        3）合成冷却为10秒，需等待冷却后才能继续合成；<br>
        4）主宠成长45以上时有一定的几率随机出神宠。<br>
        添加<span style="color:red;">守护</span>材料：
        <select class="合成道具列表" name="wp1" id="wp1" onchange="moneysum();">
          <option value="-1">请选择材料</option>
        </select>
        <br>
        添加<span style="color:red;">加成</span>材料：
        <select class="合成道具列表" name="wp2" id="wp2" onchange="moneysum();">
          <option value="-1">请选择材料</option>
        </select>
        <br>
        <table width="300" border="0" cellspacing="0" cellpadding="0" style="margin-top:10px;">
          <tbody>
            <tr>
              <td rowspan="2"><a href="#"><img src="Content/petMain/img/sdbtn.gif" alt="开始合成" onclick="Pcompose()" border="0" id="snb" class="开始合成"></a></td>
              <td>合成幸运星：</td>
            </tr>
            <tr>
              <td><a href="#"><img src="Content/petMain/img/gm15.gif" alt="合成幸运星说明" border="0" onclick="javascript:void(0);"></a></td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="help" style="position:absolute; left:493px; top:30px; width:282px; height:150px; z-index:10;padding-top:3px;display:none">
        <table width="286" border="0" cellpadding="0" cellspacing="0">
          <tbody>
          
          <tr>
            <td>
            <b><font color="green" style="font-size:12px;">合成幸运星：</font></b><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
              <tbody>
                <tr>
                  <td width="14"><img src="Content/petMain/img/bbz01.gif" width="14" height="29"></td>
                  <td style="background: url(&quot;Content/petMain/img/bbz02.gif&quot;) repeat-x; height: 29px; overflow: hidden;" heiht="2"><b><font color="green" style="font-size: 12px;">合成幸运星：</font></b></td>
                  
              
              
              
                <td width="31"><img src="Content/petMain/img/bbz03.gif" width="31" height="29"></td>
              </tr>
              </tbody>
              
            </table>
            </td>
          </tr>
          <tr>
            <td height="100" valign="top" align="left" background="Content/petMain/img/bbz04.gif" style="font-size:12px;padding:10px;padding-top:0px;line-height:1.7;"><span style="margin-left:230px;color:#1c4ec1"><a href="javascript:document.getElementById('help').style.display='none';void(0);">关闭</a></span> <span id="helptarget" style="color:#333333;"> <br>
              1.每合成失败一次，合成幸运星+1。<br>
              2.合成幸运星越多，合成成功率就越高。<br>
              3.当幸运星达到10颗时，合成率为100%。<br>
              4.每合成成功一次，合成幸运星数量归0。<br>
              5.合成成功率比以往已经大幅度提升。<br>
              </span></td>
          </tr>

          <tr>
            <td height="12"><img src="Content/petMain/img/bbz05.gif" width="286" height="12"></td>
          </tr>
          </tbody>
          
        </table>
      </div>
    </div>
    <div class="dt_task con" id="con_tab_3" style="display: none;">
      <div class="sd_hc_l">
        <div class="sd_hc_01">
          <div class="hc_pet_01" id="zsp1"><img src="" style="cursor:pointer;display:none;" id="zscp1"></div>
          <div class="hc_pet_02" id="zsp2"><img src="" style="cursor:pointer;display:none;" id="zscp2"></div>
        </div>
        <div class="sd_hc_02" id="zsxx">
          <input type="hidden" value="" id="hidd_bbalzs">
          <table width="260" border="0" cellspacing="0" cellpadding="0">
            <tbody>
              <tr>
                <td align="center"><select class="合成宠物列表" name="zsapets" id="zsapets_a" style="margin-top:3px;" onchange="selectNPPET();">
                    <option value="">请选择主宠物</option>
                  </select></td>
                <td align="center"><select class="合成宠物列表" name="zsapets" id="zsbpets_b" style="margin-top:3px;" onchange="selectNPPET();">
                    <option value="">请选择副宠物</option>
                  </select></td>
              </tr>
              <tr>
                  <td height="30" colspan="2" align="center">
                      请选择涅槃兽： <select class="合成宠物列表" onchange="selectNPPET();" name="select" id="zs">
                          <option value="">请选择涅槃兽</option>

                      </select>
                  </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="sd_hc_r"> <b>涅槃限制：</b>主副宠均需为神宠，60级以上<br>
        <b>涅槃失败惩罚：</b>无 <br>
        <b>说明：</b><br>
        1）添加的道具可以通过神秘商店、副本等获得；<br>
        2）放入涅槃兽c123可以将副宠的属性转移到主宠；<br>
        3）装备在涅槃进行的时候将会自动卸下。<br>
        添加材料一：
        <select class="合成道具列表1" name="zswp1" id="zswp1">
          <option value="-1">选择材料一</option>
        </select>
        <br>
        添加材料二：
        <select class="合成道具列表1" name="zswp2" id="zswp2">
          <option value="-1">选择材料二</option>
        </select>
        <br>
        <table width="300" border="0" cellspacing="0" cellpadding="0" style="margin-top:10px;">
          <tbody>
            <tr>
              <td><a href="#"><img id="npbtn" src="Content/petMain/img/sdbtn02.gif" alt="神宠涅槃" border="0" class="开始涅槃" onclick="zsPcompose()"></a></td>
              <td>每次涅槃消耗金币：50W</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
<div id="Layer2" style="display:none"></div>
<div style="position: absolute; left: 250px; top: 20px; z-index: 10; width: 300px; height: 280px; border: 2px solid rgb(17, 17, 68); background-color: rgb(205, 172, 61); display:none; line-height:24px; color:#653E17; padding:5px" id="infotext"> </div>
<script language="javascript">
        // Display id.
        var bball = $("#hidd_bbal").val().split(',');
        var bball_zs = $("#hidd_bbalzs").val().split(',');
        var sszsbbid = 0;
        var styles = "";
        if (styles == 'compose') {
            setTab('tab', 2, 3);
        } else if (styles == 'zs') {
            setTab('tab', 3, 3);
        }
        var setBBId = 0;
        /**
         * 显示宠物信息
         * @param {Object} obj - DOM对象
         * @param {number} id - 宠物ID
         */
        function Display(obj, id) {
            obj = obj.style;
            sel(obj);
            $.ajax({
                type: 'POST',
                url: '../Ajax_ashx/mcsd.ashx',
                data: 'bbid=' + id + '&&cztype=qh',
                success: function (msg) {
                    $(".sd_r").html(msg);
                }
            });
            
        }

        /**
         * 显示宠物详细信息
         * @param {Object} obj - DOM对象
         * @param {number} id - 宠物ID
         * @param {number} level - 宠物等级
         * @param {number} czl - 成长率
         * @param {string} type - 宠物类型
         */
        function Display1(obj, id, level, czl, type) {
            setBBId = id;
            sszsbbid = 0;
            obj = obj.style;
            sel(obj);
            if (level > 0 && type == 1) {
                $('bblevel').innerHTML = level;
                $('bbczl').innerHTML = czl;
            } else if (type == 2) {
                $('bblevel').innerHTML = '';
                $('bbczl').innerHTML = '';
            }
        }

        /**
         * 复制文字到剪贴板
         * @param {string} words - 要复制的文字
         */
        function copyWord(words) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(words).then(() => {
                    showMessage('已复制到剪贴板', 'success');
                }).catch(() => {
                    showMessage('复制失败', 'error');
                });
            } else {
                showMessage('浏览器不支持剪贴板操作', 'warning');
            }
        }
        /**
         * 宠物进化功能
         * @param {string} jhtype - 进化类型 ('1'=A型进化, '2'=B型进化)
         */
        async function JinHua(jhtype) {
            try {
                if (!sxcw || sxcw === -1) {
                    showMessage("请先选择要进化的宠物！", "warning");
                    return;
                }

                // 显示加载状态
                if (window.petSynthesisApi && window.petSynthesisApi.showLoading) {
                    window.petSynthesisApi.showLoading("正在进化宠物...");
                }

                // 调用新的进化API
                const headers = {
                    'Content-Type': 'application/json',
                    'X-User-Id': '1' // 临时使用固定用户ID
                };

                const response = await fetch('/api/PetEvolution/evolve', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        UserPetId: parseInt(sxcw),
                        EvolutionType: jhtype === '1' ? 'A' : 'B'
                    })
                });

                // 隐藏加载状态
                if (window.petSynthesisApi && window.petSynthesisApi.hideLoading) {
                    window.petSynthesisApi.hideLoading();
                }

                // 检查HTTP状态码
                if (response.status === 401) {
                    console.warn('用户未登录，跳转到登录页面');
                    handleUnauthorized();
                    return;
                }

                const result = await response.json();

                // 检查是否是未授权错误 (兼容大小写)
                if ((!result.Success || !result.success) &&
                    (result.ErrorCode === 'UNAUTHORIZED' || result.errorCode === 'UNAUTHORIZED')) {
                    console.warn('用户未登录或Token无效，跳转到登录页面');
                    handleUnauthorized();
                    return;
                }

                // 兼容大小写字段名检查
                if (result.Success || result.success) {
                    console.log('🎉 进化成功！处理成功响应...');
                    console.log('🔍 响应数据:', result);

                    // 兼容大小写获取数据
                    const data = result.Data || result.data;
                    const growthIncrease = data?.GrowthIncrease || data?.growthIncrease;
                    const message = data?.Message || data?.message || result.Message || result.message;

                    let successMessage = message || "恭喜您，宝贝进化成功！！";
                    if (growthIncrease) {
                        successMessage += ` 成长增加: ${growthIncrease}`;
                    }

                    console.log('✅ 显示成功消息:', successMessage);

                    showMessage(successMessage, "success");

                    // 刷新页面数据
                    await loadHechengPet();
                    // 重新加载道具列表
                    await loadHechengProp();
                    // 重新获取当前宠物的进化信息
                    if (sxcw) {
                        const currentPetElement = document.querySelector(`[data-pet-id="${sxcw}"]`);
                        if (currentPetElement) {
                            await getJH(sxcw, currentPetElement);
                        }
                    }
                } else {
                    console.error('❌ 进化失败，处理错误响应...');
                    console.log('🔍 错误响应数据:', result);

                    const errorMessage = result.Message || result.message || "进化失败，请重试！";
                    console.log('❌ 显示错误消息:', errorMessage);

                    showMessage(errorMessage, "error");
                }
            } catch (error) {
                console.error('进化失败:', error);
                if (window.petSynthesisApi && window.petSynthesisApi.hideLoading) {
                    window.petSynthesisApi.hideLoading();
                }

                const errorMessage = "进化失败，请重试！";
                showMessage(errorMessage, "error");
            }
        }



        /**
         * 设置宠物选中状态的视觉效果
         * @param {Object} obj - 选中的DOM对象
         */
        function sel(obj) {
            for (var i = 1; i < 4; i++) {
                try {
                    document.getElementById("i"+i).style.opacity = "0.5";
                    document.getElementById("s" + i).style.opacity = "0.5";
                    document.getElementById("z" + i).style.opacity = "0.5";

                    
                } catch (e) {
                    var x = e;
                    continue;
                }
            }
            if (document.all) {
                obj.filter = "alpha(opacity=100)";
            } else {
                obj.opacity = 1;
            }
        }



        /**
         * 宠物合成功能
         * 执行宠物合成操作，包含新API调用和错误处理
         */
        async function Pcompose() {
            var prop1 = $("#wp1 option:selected").val();
            var prop2 = $("#wp2 option:selected").val();

            if (APet == -1) {
                showMessage("请选择一个主宠！", "warning");
	            return;
            }else if (BPet == -1){
                showMessage("请选择一个副宠！", "warning");
	            return;
            } else if (APet == BPet) {
                showMessage("主副宠不能选择同一个！", "warning");
	            return;
            } else {
                // 使用新的API进行合成
                try {
                    // 准备道具列表
                    var usedItems = [];
                    if (prop1 && prop1 !== '-1') usedItems.push(prop1);
                    if (prop2 && prop2 !== '-1') usedItems.push(prop2);

                    // 先验证合成条件
                    const validation = await window.petSynthesisApi.validateSynthesis(APet, BPet);
                    if (!validation.success) {
                        showMessage(validation.message, "error");
                        return;
                    }

                    // 执行合成
                    const result = await window.petSynthesisApi.synthesizePet(APet, BPet, usedItems);

                    if (result.success) {
                        let message = "合成成功！";
                        if (result.data.isGodPet) {
                            message += " 恭喜获得神宠！";
                        }
                        message += ` 成长增加: ${result.data.growthIncrease}`;
                        showMessage(message, "success");

                        // 刷新页面数据
                        await loadHechengPet();
                        // 重新加载道具列表
                        await loadHechengProp();
                    } else {
                        showMessage(result.message, "error");
                    }
                } catch (error) {
                    console.error('合成失败:', error);
                    showMessage("合成失败，请重试！", "error");
                }
            }
        }

        /**
         * 计算合成所需金币总数
         * @returns {Promise<number>} 合成所需的金币数量
         */
        async function moneysum() {
            var apid = document.getElementById('comapets_a').options[document.getElementById('comapets_a').selectedIndex].value;
            var bpid = document.getElementById('comapets_b').options[document.getElementById('comapets_b').selectedIndex].value;

            console.log(`💰 计算合成金币: apid=${apid}, bpid=${bpid}`);

            // 注意：不再在这里更新宠物图片，图片更新由 selectHCPET() 函数负责
            // 这样避免了重复更新和可能的图片错误覆盖问题

            // 如果选择了有效的宠物，调用API获取准确的金币需求
            if (apid && bpid && apid !== '' && bpid !== '' && apid !== '-1' && bpid !== '-1') {
                try {
                    const config = await window.petSynthesisApi.getSynthesisConfig(apid, bpid);
                    if (config.success && config.data && config.data.costGold) {
                        document.getElementById('smoney').innerHTML = config.data.costGold + '金币！';
                        return config.data.costGold;
                    }
                } catch (error) {
                    console.error('获取金币需求失败:', error);
                }
            }

            // 默认金币消耗（兼容原有逻辑）
            var p1 = document.getElementById('wp1').options[document.getElementById('wp1').selectedIndex].text.split('-')[1];
            var p2 = document.getElementById('wp2').options[document.getElementById('wp2').selectedIndex].text.split('-')[1];
            if (typeof ap == 'undefined' || typeof bp == 'undefined') return false;
            if (p1 == '' || typeof p1 == 'undefined') p1 = 0;
            if (p2 == '' || typeof p2 == 'undefined') p2 = 0;

            // 使用默认的50000金币
            document.getElementById('smoney').innerHTML = 50000 + '金币！';
            return 50000;
        }

        /**
         * 播放Flash动画效果
         * @param {number} tf - 动画类型标识
         * @param {string} style - 样式参数
         */
        function playF(tf, style) {
            var oPopup = window.createPopup();
            var fname = '';
            if (tf == 1) fname = 'hcdh'; //ok
            else fname = 'hcdh0';
            var obj = document.getElementById('Layer2');
            with (obj) {
                style.backgroundColor = "#dfd495";
                style.display = 'block';
                //style.border="solid black 1px";
                innerHTML = '<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" '
                           + 'codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0" width="305" height="185">'
                           + '<param name="movie" value="../images/ui/compose/' + fname + '.swf" />'
                           + '<param name="quality" value="high" />'
                           + '<param name="wmode" value="transparent" />'
                           + '<embed src="' + IMAGE_SRC_URL + '/ui/compose/' + fname + '.swf" quality="high" ' + 'pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="305" height="185"></embed></object>';
            }

            window.setTimeout(function () { document.getElementById('Layer2').style.display = 'none'; }, 5000);
            //window.parent.$('gw').src='./function/City_Mod.php';
        }

        /**
         * 神宠涅槃合成功能
         * 执行神宠涅槃操作，使用新的API接口，包含完善的用户体验
         */
        async function zsPcompose() {
            var zspet1 = CPet;
            var zspet2 = DPet;
            var zsprop1 = $("#zswp1 option:selected").val();
            var zsprop2 = $("#zswp2 option:selected").val();
            var pn = EPet;

            // 基础验证
            if (zspet1 == -1) {
                showMessage("请选择一个主宠！", "warning");
                return;
            } else if (zspet2 == -1) {
                showMessage("请选择一个副宠！", "warning");
                return;
            } else if (zspet1 == zspet2) {
                showMessage("主副宠不能选择同一个！", "warning");
                return;
            } else if (pn == -1) {
                showMessage("请选择涅槃兽", "warning");
                return;
            }

            // 道具确认提示
            let hasProtectionItem = zsprop1 && zsprop1 !== '-1' &&
                                  (zsprop1 === '524' || $("#zswp1 option:selected").text().includes('护宠'));

            if (!hasProtectionItem) {
                if (!confirm('您没有添加转生保护道具，转生失败会造成副宠消失。确定要继续转生吗？')) {
                    return;
                }
            }

            // 装备提醒
            if (!confirm('如果您宠物身上有装备，转生后装备会消失，是否确定转生？')) {
                return;
            }

            try {
                // 禁用按钮防止重复点击
                const nirvanaBtn = document.getElementById('npbtn');
                if (nirvanaBtn) {
                    nirvanaBtn.style.pointerEvents = 'none';
                    nirvanaBtn.style.opacity = '0.5';
                }

                // 显示处理中提示
                showMessage("转生处理中，请稍候...", "info");

                // 使用新的涅槃API，支持两个道具（对应老系统的道具1和道具2）
                const userId = getCurrentUserId();
                const requestData = {
                    UserId: userId,
                    MainPetId: parseInt(zspet1),
                    SubPetId: parseInt(zspet2),
                    NirvanaPetId: parseInt(pn),
                    UsedItemId1: zsprop1 && zsprop1 !== '-1' ? zsprop1 : null,
                    UsedItemId2: zsprop2 && zsprop2 !== '-1' ? zsprop2 : null,
                    NirvanaType: 'NORMAL'
                };

                console.log('🔄 发送转生请求:', requestData);

                const response = await fetch('/api/Nirvana/execute', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (result.success && result.data) {
                    if (result.data.isSuccess) {
                        // 转生成功
                        let message = `🎉 转生成功！\n`;
                        message += `✨ 获得宠物：${result.data.resultPet?.name || '新宠物'}\n`;
                        if (result.data.resultGrowth) {
                            message += `📈 成长增加：${result.data.resultGrowth.toFixed(2)}\n`;
                        }
                        message += `💰 消耗金币：${result.data.costGold?.toLocaleString() || '500,000'}\n`;
                        if (result.data.vipBonus > 0) {
                            message += `👑 VIP加成：${(result.data.vipBonus * 100).toFixed(1)}%`;
                        }

                        showMessage(message, "success");

                        // 播放成功动画
                        playF(1, 'zs');
                    } else {
                        // 转生失败
                        let message = `💔 转生失败！\n`;
                        message += result.data.message || "副宠和涅槃兽已消失";

                        showMessage(message, "error");

                        // 播放失败动画
                        playF(0, 'zs');
                    }

                    // 刷新页面数据
                    setTimeout(async () => {
                        await loadHechengPet();
                        // 重新加载道具列表
                        await loadHechengProp();
                    }, 2000);
                } else {
                    showMessage(result.message || "转生失败，请重试！", "error");
                }
            } catch (error) {
                console.error('涅槃失败:', error);
                showMessage("网络错误，转生失败，请重试！", "error");
            } finally {
                // 恢复按钮状态
                setTimeout(() => {
                    const nirvanaBtn = document.getElementById('npbtn');
                    if (nirvanaBtn) {
                        nirvanaBtn.style.pointerEvents = 'auto';
                        nirvanaBtn.style.opacity = '1';
                    }
                }, 3000);

                window.parent.jid = null;
                window.parent.jname = "普通攻击";
            }
        }

        /**
         * 更新HTML显示内容
         * @param {string} mg - 包含更新信息的字符串
         */
        function upHtml(mg) {
            
            var array = mg.split("^");
            $("#zsp1").html("<img src=' .image_src_url.'=' bb=' k119.gif'=' onclick='Display(239934);' style='cursor:pointer;display:none;' id='zscp1'>");
            $("#zsp2").html("<img src=' .image_src_url.'=' bb=' k119.gif'=' onclick='Display(239934);' style='cursor:pointer;display:none;' id='zscp2'>");
            $("#comp1").html("<img src=' .image_src_url.'=' bb=' k119.gif'=' onclick='Display(239934);' style='cursor:pointer;display:none;' id='cp1'>");
            $("#comp2").html("<img src=' .image_src_url.'=' bb=' k119.gif'=' onclick='Display(239934);' style='cursor:pointer;display:none;' id='cp1'>");
            $(".sd_l").html(array[0]);
            $(".sd_r").html(array[1]);
            $("#hcxx").html(array[2]);
            $("#wp1").html(array[3]);
            $("#wp2").html(array[3]);
            $("#zsxx").html(array[4]);
            $("#zswp1").html(array[5]);
            $("#zswp2").html(array[6]);
            showMessage(array[7], "info");
        }
        /**
         * 计算涅槃所需金币总数
         * @returns {Promise<number>} 涅槃所需的金币数量
         */
        async function zsmoneysum() {
            var ap = document.getElementById('zsapets_a').options[document.getElementById('zsapets_a').selectedIndex].text.split('-')[1];
            var bp = document.getElementById('zsbpets_b').options[document.getElementById('zsbpets_b').selectedIndex].text.split('-')[1];
            var apid = document.getElementById('zsapets_a').options[document.getElementById('zsapets_a').selectedIndex].value;
            var bpid = document.getElementById('zsbpets_b').options[document.getElementById('zsbpets_b').selectedIndex].value;

            // 更新宠物图片显示
            if (typeof apid != 'undefined' && apid != '') {
                for (var i = 0; i < bball_zs.length; i++) {
                    var img1 = bball_zs[i].split('-')[1];
                    if (bball_zs[i].split('-')[0] == apid) {
                        document.getElementById('zscp1').src = "/game/pages/Content/PetPhoto/" + img1;
                        document.getElementById('zscp1').style.display = '';
                    }
                }
            }
            if (typeof bpid != 'undefined' && bpid != '') {
                for (var i = 0; i < bball_zs.length; i++) {
                    var img1 = bball_zs[i].split('-')[1];
                    if (bball_zs[i].split('-')[0] == bpid) {
                        document.getElementById('zscp2').src = "/game/pages/Content/PetPhoto/" + img1;
                        document.getElementById('zscp2').style.display = '';
                    }
                }
            }

            // 通过API获取涅槃配置信息
            try {
                if (CPet !== -1 && DPet !== -1 && EPet !== -1) {
                    const userId = getCurrentUserId();
                    const response = await fetch('/api/Nirvana/preview', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            UserId: userId,
                            MainPetId: parseInt(CPet),
                            SubPetId: parseInt(DPet),
                            NirvanaPetId: parseInt(EPet)
                        })
                    });

                    const result = await response.json();
                    if (result.isSuccess && result.data) {
                        return result.data.estimatedCost || 500000; // 返回API提供的金币消耗
                    }
                }
                return 500000; // 默认涅槃金币消耗
            } catch (error) {
                console.error('获取涅槃金币需求失败:', error);
                return 500000; // 默认涅槃金币消耗
            }
        }

        /**
         * 神宠进化功能
         * 执行神宠的进化操作
         */
        function ssJinHua() {
            if (setBBId == 0 || typeof (bbjs[setBBId]) == 'undefined') {
                showMessage('请选择宠物', 'warning');
                return;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    if (t.responseText == 'OK') {
                        window.parent.Alert('进化成功');
                        rd();
                    }
                    else {
                        window.parent.Alert(t.responseText);
                    }
                },
                asynchronous: true
            };
	        var str = bbjs[setBBId];
            var strs = str.split('|');
            if (strs[6] != 7) {
                parent.Alert("请确认您的宠物是否为神圣宠物!");
                return;
            }
            if (parseInt(strs[7]) <= parseInt(strs[5])) {
                if (!confirm('您的宠物已经达到成长上限，进化不会产生任何变化，但会消耗材料及游戏币。')) {
                    return;
                }
            }
            var ajax = new Ajax.Request('../function/superJhGate.php?pid=' + setBBId + '&zjsxdj=' + $('zjsxdj').value, opt);
        }

        var bbjs = {};
        bbjs["239934"] = "N/A|83|N/A|N/A|0|22|6";
        bbjs["239928"] = "N/A|3|N/A|N/A|0|1.3|1";


        /**
         * 显示宠物进化信息
         * @param {number} bid - 宠物ID
         */
        function showJHInfo(bid) {
            var str = bbjs[bid];
            var strs = str.split('|');
            var html = '<table width="210" cellspacing="0" cellpadding="0" border="0">\
			  <tbody><tr>\
				<td width="125">进化需要等级：'+ strs[0] + '</td>\
				<td width="85">当前等级：'+ (strs[6] < 7 ? 'N/A' : strs[1]) + '</td>\
				</tr>\
			  <tr>\
				<td colspan="2">进化所需材料：'+ strs[2] + '</td>\
				</tr>\
			  <tr>\
				<td colspan="2">进化所需金币：'+ strs[3] + '</td>\
				</tr>\
			  <tr>\
				<td colspan="2">当前进化次数：'+ (strs[6] < 7 ? 'N/A' : strs[4]) + ' <select name="zjsxdj" id="zjsxdj"><option>增加属性道具</option>' + '' + '</select></td>\
			  </tr>\
			  <tr>\
				<td><img width="79" height="24" src="../images/sd_cion01.jpg" style="cursor:pointer" onclick="displayInfo(0)" ></td>\
				<td><img width="79" height="24" src="../images/sd_cion02.jpg"  style="cursor:pointer" onclick="ssJinHua()"></td>\
			  </tr>\
			</tbody></table>';
            $('jhxq').innerHTML = html;
            var nm = '不可抽取';

            if (strs[6] < 7) {
                if (strs[5] > 600) {
                    nm = 600 * 10000;
                } else {
                    nm = strs[5] * 10000;
                }
            }

            $('cqjb').innerHTML = parseInt(nm);

            /*if(strs[6]==7) $('hascz').innerHTML=strs[5];
            else $('hascz').innerHTML='不可转化';*/
        }

        var infs = [
        '1.每一个神圣宠物最多能进化10次；<br/>\
2.神圣宠物的进化必须满足进化等级要求；<br/>\
3.进化成功之后，宠物等级变为1级；<br/>\
4.当宠物成长已经达到该阶段成长上限时，进化不会再增加其成长。<br/>\
',
        '1.	只有神宠能使用成长抽取功能；<br/>\
2.	神宠的成长抽取按照不同阶段抽取的比例不同，成长越高，抽取的比例越高；<br/>\
3.	所抽取的成长最大不能超过600，超过600时只按600计算；<br/>\
4.	可以通过添加道具的方式增加成长抽取比例（可以在副本中获得道具碎片，也可以在神秘商店中直接购买道具）；<br/>\
5.	抽取完成后宠物将会消失。<br/>\
',
        '1.只有神圣宠物才能接受成长的直接转化；<br/>\
2.转化成长，需要手动输入转化数字，只能输入整数；<br/>\
3.当转化成长率超过该神圣宠物当前上限时，多出的成长将消失。<br/>\
',
        '1.神圣宠物通过转生可以提高其成长上限及等级上限；<br />\
2.转生需要满足转生等级及成长要求，同时还需要有对应的转生道具；<br />\
3.转生并不是百分之百成功，一旦失败，转生材料将会消失；<br />\
4.可以通过购买的增加成功率道具来增加转生的成功几率。<br />\
'
        ];

        /**
         * 显示信息提示框
         * @param {number} id - 信息ID
         */
        function displayInfo(id) {
            $('infotext').innerHTML = '' + infs[id] + '<br/><input type="button" value="关闭" onclick="$(\'infotext\').style.display=\'none\'" style="position:absolute;left:135px">';
            $('infotext').style.display = 'block';
        }


        /**
         * 抽取功能
         * 执行宠物抽取操作
         */
        function chouqu() {
            if (setBBId == 0 || typeof (bbjs[setBBId]) == 'undefined') window.parent.Alert('请选择宠物');
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    if (t.responseText.substr(0, 2) == 'OK') {
                        window.parent.Alert('抽取成功,抽取获得了:' + t.responseText.substr(2, 6) + '成长');
                        //rd();
                        window.location = '/function/Sd_Mod.php';
                    }
                    else {
                        window.parent.Alert(t.responseText);
                    }
                },
                asynchronous: true
            };
	        var str = bbjs[setBBId];
            var strs = str.split('|');
            /*
            if(strs[6]!=6){
                if(strs[5]<40){
                    parent.Alert("五系宠物成长小于40不能抽取!");
                    return;
                }
            }
            */

            if (strs[5] < 30) {
                parent.Alert("成长小于30不能抽取!");
                return;
            }
            if (strs[6] != 6) {
                if (!confirm('抽取成长在5%-15%之间随机一个数值\n\n确认宠物装备是否脱下，否则抽取完装备将会消失\n\n抽取完成之后宠物将消失。\n\n确定么?')) return;
            } else {
                if (strs[5] < 65) {
                    swapRate = "10%-20%之间随机一个数值";
                }
                else if (strs[5] < 85) {
                    swapRate = "30%-50%之间随机一个数值";
                }
                else if (strs[5] < 100) {
                    swapRate = "50%-65%之间随机一个数值";
                }
                else if (strs[5] < 110) {
                    swapRate = "65%";
                }
                else if (strs[5] < 115) {
                    swapRate = "70%";
                }
                else if (strs[5] < 120) {
                    swapRate = "75%";
                }
                else {
                    swapRate = "80%";
                }
                if (!confirm('抽取成长比例：' + swapRate + '(不包含道具的效果)\n\n抽取完成后，宠物不会消失，成长变为1\n\n已经抽取过成长的神宠不能进行涅槃，不能进行传承。,\n\n确定么?')) return;
            }
            var ajax = new Ajax.Request('../function/cqGate.php?act=cq&pid=' + setBBId + "&pid1=" + $('zjcqbldj1').value + "&pid2=" + $('zjcqbldj2').value, opt);
        }

        /**
         * 转化功能
         * 执行宠物转化操作
         */
        function zhuanhua() {
            if (setBBId == 0 || typeof (bbjs[setBBId]) == 'undefined') window.parent.Alert('请选择宠物');
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    if (t.responseText == 'OK') {
                        window.parent.Alert('转化成功');
                        rd();
                    }
                    else {
                        window.parent.Alert(t.responseText);
                    }
                },
                asynchronous: true
            };
	        var str = bbjs[setBBId];
            var strs = str.split('|');
            if (strs[6] != 7) {
                parent.Alert("该宠物不能进行转化!");
                return;
            }
            if (parseInt($('zhvalue').value) < 1 || isNaN(parseInt($('zhvalue').value))) {
                parent.Alert("请输入你要转化的点数,只允许数字!");
                return;
            } else {
                $('zhvalue').value = parseInt($('zhvalue').value);
            }
            if (parseInt(strs[7]) < parseInt($('zhvalue').value) + parseInt(strs[5])) {
                if (!confirm('抽取的成长加原有成长超出最大成长上限' + (parseInt($('zhvalue').value) + parseInt(strs[5]) - parseInt(strs[7])) + '点，多出的成长将会消失,确定要转化？')) {
                    return;
                }
            }

            var ajax = new Ajax.Request('../function/zhGate.php?act=zh&pid=' + setBBId + "&v=" + $('zhvalue').value, opt);
        }

        /**
         * 显示神宠转生信息
         * @param {number} bid - 宠物ID
         */
        function sszsshow(bid) {
            if (bid == 0) {
                $('sszsimg').innerHTML = '';
                return;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var res = t.responseText;
                    $('sszsimg').innerHTML = res;
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/sszsInfo.php?id=' + bid + '&op=img', opt);
        }

        /**
         * 处理神宠转生字符串信息
         * @param {number} bid - 宠物ID
         * @param {number} sid - 转生ID
         * @param {Object} obj - DOM对象
         */
        function sszsstr(bid, sid, obj) {
            if (bid == 0 && sid == 0) {
                //$('sszsstr').innerHTML = '';
                //$('sszsstr1').style.display = 'none';
                return;
            }

            for (var i = 1; i < 10; i++) {
                try {
                    if (bid != i) seta($('p_p' + i).getElementsByTagName('img')[0].style, 0.5);
                    else seta($('p_p' + i).getElementsByTagName('img')[0].style, 1);
                } catch (e) { }
            }
            seta(obj.style, 1);
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var res = t.responseText;
                    $('sszsstr').innerHTML = res;
                    $('sszsstr1').style.display = 'block';
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/sszsInfo.php?id=' + bid + '&sid=' + sid + '&op=str', opt);
        }

        /**
         * 神宠转生功能
         * 执行神宠转生操作
         */
        function sszs() {
            var wp1 = $('sszswp1').options[$('sszswp1').selectedIndex].value;
            var wp2 = $('sszswp2').options[$('sszswp2').selectedIndex].value;
            if (wp1 == "" && wp2 == "") {
                if (!confirm('未放入成功率道具，可能会导致失败材料消失，\n\n如果您宠物身上有装备，转生后装备会消失，\n\n您确定要转生吗？')) {
                    return;
                }
            } else if (!confirm('如果您宠物身上有装备，转生后装备会消失，是否确定转生？')) {
                return;
            }
            var opt = {
                method: 'get',
                onSuccess: function (t) {
                    var res = t.responseText;
                    if (res == 5) {
                        playF(1, 'sszs');
                    } else if (res == 6) {
                        playF(0, 'sszs');
                    } else {
                        window.parent.Alert(res);
                    }
                },
                on404: function (t) {
                },
                onFailure: function (t) {
                },
                asynchronous: true
            };
	        var ajax = new Ajax.Request('../function/sszsInfo.php?old=' + setBBId + '&newid=' + sszsbbid + '&wp1=' + wp1 + '&wp2=' + wp2 + '&op=zs', opt);
        }
        /**
         * 重新加载页面
         */
        function rd() {
            parent.$('gw').contentWindow.location.reload();
        }

        /**
         * 设置透明度
         * @param {Object} sty - 样式对象
         * @param {number} v - 透明度值 (0-1)
         */
        function seta(sty, v) {
            if (document.all) {
                sty.filter = "alpha(opacity=" + v * 100 + ")";
            } else {
                sty.opacity = v;
            }
        }
        setBBId = 239934; sszsshow(0); sszsstr(0, 0, this);

        // 页面加载完成后检查用户登录状态
        $(document).ready(function() {
            console.log('📋 petMain.html 页面加载完成');

            // 检查用户登录状态
            checkUserLoginStatus();

            // 如果认证管理器可用，监听登录状态变化
            if (window.authManager) {
                window.authManager.onLogin(function(userData) {
                    console.log('👤 用户登录状态变化:', userData);
                    updateUserDisplay(userData);
                });

                window.authManager.onLogout(function() {
                    console.log('👤 用户已登出');
                    // 可以在这里处理登出后的逻辑
                });
            }
        });
</script>
