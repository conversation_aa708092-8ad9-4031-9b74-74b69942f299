using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;

namespace WebControlSwitching
{
    public partial class Form1 : Form
    {
        private ScriptInterface scriptInterface;
        private WebView2 webView2Control;
        private bool isWebView2Active = false;

        public Form1()
        {
            InitializeComponent();
            scriptInterface = new ScriptInterface(this);

            // 初始化WebBrowser
            webBrowser1.ObjectForScripting = scriptInterface;
            webBrowser1.DocumentCompleted += WebBrowser1_DocumentCompleted;

            // 初始化WebView2
            InitializeWebView2();
            
            // 默认显示WebBrowser
            SwitchToWebBrowser();
        }

        private async void InitializeWebView2()
        {
            try
            {
                webView2Control = new WebView2();
                webView2Control.Dock = DockStyle.Fill;
                webView2Panel.Controls.Add(webView2Control);

                await webView2Control.EnsureCoreWebView2Async(null);
                webView2Control.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
                
                LoadWebView2Page();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"WebView2初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void WebBrowser1_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            if (e.Url.ToString() == webBrowser1.Url.ToString())
            {
                webBrowser1.ObjectForScripting = scriptInterface;
            }
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                string message = e.TryGetWebMessageAsString();

                if (message == "switchToWebBrowser")
                {
                    this.Invoke(new Action(() => SwitchToWebBrowser()));
                }
            }
            catch (Exception ex)
            {
                this.Invoke(new Action(() => MessageBox.Show($"处理WebView2消息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)));
            }
        }

        private void LoadWebBrowserPage()
        {
            try
            {
                string htmlPath = Path.Combine(Application.StartupPath, "webbrowser.html");
                if (File.Exists(htmlPath))
                {
                    webBrowser1.Navigate(htmlPath);
                }
                else
                {
                    MessageBox.Show($"找不到文件: {htmlPath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载WebBrowser页面失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadWebView2Page()
        {
            try
            {
                string htmlPath = Path.Combine(Application.StartupPath, "webview2.html");
                if (File.Exists(htmlPath) && webView2Control?.CoreWebView2 != null)
                {
                    string fileUri = new Uri(htmlPath).ToString();
                    webView2Control.CoreWebView2.Navigate(fileUri);
                }
                else if (!File.Exists(htmlPath))
                {
                    MessageBox.Show($"找不到文件: {htmlPath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载WebView2页面失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 控件切换方法
        public void SwitchToWebBrowser()
        {
            webBrowser1.Visible = true;
            webView2Panel.Visible = false;
            isWebView2Active = false;
            this.Text = "WebBrowser控件 - 双控件切换示例";
            LoadWebBrowserPage();
        }

        public void SwitchToWebView2()
        {
            webBrowser1.Visible = false;
            webView2Panel.Visible = true;
            isWebView2Active = true;
            this.Text = "WebView2控件 - 双控件切换示例";
            LoadWebView2Page();
        }

    }

    // 简化的脚本接口
    [ComVisible(true)]
    public class ScriptInterface
    {
        private Form1 mainForm;

        public ScriptInterface(Form1 form)
        {
            mainForm = form;
        }

        public void SwitchToWebView2()
        {
            if (mainForm != null)
            {
                mainForm.SwitchToWebView2();
            }
        }

        public void SwitchToWebBrowser()
        {
            if (mainForm != null)
            {
                mainForm.SwitchToWebBrowser();
            }
        }
    }
}
