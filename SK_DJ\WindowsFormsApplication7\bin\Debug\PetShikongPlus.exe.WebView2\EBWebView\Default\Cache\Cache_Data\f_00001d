<html xmlns="http://www.w3.org/1999/xhtml"><head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>petInfo</title>
    <link href="Content/CSS/PetInfo.css" rel="stylesheet">
    <script src="Content/Javascript/jquery-1.8.3.min.js"></script>
    <script src="./Content/Javascript/petList.js?123"></script>
    <script src="../js/cache-manager.js"></script>
    <script src="../js/loading-manager.js?v=7"></script>
    <script src="../js/auth-manager.js"></script>

    <script>
        // 页面级别：抑制初始化“正在获取宠物信息...”的加载遮罩
        (function(){
            if (window.loadingManager && typeof window.loadingManager.suppress === 'function') {
                // window.loadingManager.suppress('正在获取宠物信息');
            }
        })();
    </script>

    <script src="../js/pet-info-api-adapter.js?b=6"></script>
    <style>
        .ppp1 .petPng {
            POSITION: ABSOLUTE;
            TOP: -110PX;
            LEFT: -20pX;
        }

        .ppp2 .petPng {
            POSITION: ABSOLUTE;
            TOP: -50pX;
        }

        .ppp3 .petPng {
            POSITION: ABSOLUTE;
            TOP: -50pX;
        }

        .yingbi {
            POSITION: ABSOLUTE;
            width: 1px;
            height: 1px;
        }
    </style>
    <script type="text/javascript">
        var zbList;
        var tzList;

        /**
         * 独立加载宠物装备数据
         * @param {number} petId - 宠物id
         * @param {number} userId - 用户ID
         */
        async function loadPetEquipments(petId, userId) {
            try {
                console.log('独立加载宠物装备数据 - 宠物id:', petId, '用户ID:', userId);

                const response = await fetch(`/api/Equipment/pet/${petId}/user/${userId}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();
                console.log('装备数据API响应:', result);

                if (result.success && result.data) {
                    console.log('装备信息:', result.data);
                    loadZB(result.data);
                } else {
                    console.log('无装备数据，清空装备显示');
                    loadZB([]);
                }
            } catch (error) {
                console.error('获取装备数据失败:', error);
                loadZB([]);
            }
        }

        /**
         * 页面加载初始化函数
         * 检查外部接口状态，用于验证页面是否在正确的环境中运行
         */
        function loadIF() {
            // 检查是否在浏览器环境中运行
            if (typeof window.external !== 'undefined' &&
                typeof window.external.check === 'function' &&
                window.external.check() == "true") {
                // 检查通过，页面可以正常运行
                console.log('外部接口检查通过');
            } else {
                // 在浏览器环境中运行，跳过外部接口检查
                console.log('在浏览器环境中运行，跳过外部接口检查');
            }
        }

        /**
         * 消息接收函数
         * 用于接收和显示来自外部的消息（调试用）
         * @param {string} msg - 要显示的消息内容
         */
        function text(msg) {
            alert("recv:" + msg);
        }

        /**
         * 页面主动初始化函数
         * 在页面加载完成后主动获取宠物信息数据
         */
        async function initializePage() {
            try {
                // 检查是否有API适配器
                if (typeof window.external !== 'undefined' && typeof window.external.getPetInfoPageData !== 'undefined') {
                    console.log('开始初始化宠物信息页面...');

                    // 检查登录状态
                    if (window.authManager && !window.authManager.isLoggedIn()) {
                        console.warn('⚠️ 用户未登录，将使用测试模式');
                        // 可以选择重定向到登录页面或显示登录提示
                        // window.location.href = '/login.html';
                    }

                    // 获取用户ID和用户信息
                    const userId = getCurrentUserId();
                    const currentUser = getCurrentUser();

                    console.log('当前用户信息:', { userId, currentUser });

                    // 调用API获取完整的宠物信息页面数据（关闭初始化加载弹框）
                    const result = await window.external.getPetInfoPageData(userId, { showLoading: false });

                    if (result && result.success && result.data) {
                        console.log('获取宠物信息成功，开始更新页面...');
                        console.log('API返回的数据:', result.data);

                        // 转换用户信息格式
                        if (result.data.player) {
                            // 优先使用认证管理器中的用户信息
                            const authUser = getCurrentUser();
                            const userInfo = {
                                名字: authUser?.nickname || result.data.player.nickname || result.data.player.account || `用户${userId}`,
                                金币: result.data.player.gold || 0,
                                水晶: result.data.player.crystal  || 0,
                                元宝: result.data.player.diamond|| 0, 
                                主宠名字: result.data.mainPet ? result.data.mainPet.name : '',
                                宠物数量: result.data.pets ? result.data.pets.length : 0,
                                sex: result.data.player.sex || '未知' // 正确映射性别字段
                            };
                            console.log('转换后的用户信息:', userInfo);
                            showUserInfo(JSON.stringify(userInfo));
                        }

                        // 保存宠物技能数据
                        if (result.data.petSkills) {
                            allPetSkills = result.data.petSkills;
                            console.log('保存宠物技能数据:', allPetSkills);
                        }

                        // 转换宠物列表格式
                        if (result.data.pets && result.data.pets.length > 0) {
                            const petList = result.data.pets.map(pet => {
                                // 获取该宠物的技能信息
                                const petSkills = allPetSkills[pet.id] || [];
                                const skillDisplay = petSkills.length > 0
                                    ? petSkills.map(skill => (skill.skillConfig && skill.skillConfig.skillName) || '未知技能').join(', ')
                                    : '无技能';

                                return {
                                    宠物序号: pet.id,
                                    宠物名字: pet.name, // 添加宠物名字
                                    等级: pet.level,
                                    五行: pet.element || '无',
                                    境界: pet.realm || '无',
                                    生命: pet.hp,
                                    魔法: pet.mp,
                                    最大生命: pet.hp, // 假设当前生命等于最大生命
                                    最大魔法: pet.mp, // 假设当前魔法等于最大魔法
                                    成长: pet.growth,
                                    形象: pet.petNo || pet.pet_no || 264, // 使用petNo作为形象ID，提供默认值
                                    状态: pet.isMain ? "0" : "1", // 0表示主战宠物
                                    已进化次数: 0, // API中没有此信息
                                    加深: 0,
                                    抵消: 0,
                                    吸血: 0,
                                    技能显示: skillDisplay, // 显示技能信息
                                    技能列表: petSkills, // 完整的技能列表
                                    指定形象: null
                                };
                            });

                            console.log('转换后的宠物列表:', petList);
                            readPet(JSON.stringify(petList));
                        }

                        // 装备信息通过独立接口获取
                        if (result.data.mainPet) {
                            loadPetEquipments(result.data.mainPet.id, getCurrentUserId());
                        } else {
                            console.log('无主宠物数据，清空装备显示');
                            loadZB([]);
                        }

                        console.log('页面初始化完成');
                        // 恢复加载提示（仅抑制初始化的提示）
                        if (window.loadingManager && typeof window.loadingManager.unsuppress === 'function') {
                            // window.loadingManager.unsuppress('正在获取宠物信息');
                        }
                    } else {
                        console.error('获取宠物信息失败:', result?.message || '未知错误');
                        // 显示错误提示
                        if (window.loadingManager) {
                            window.loadingManager.showError('获取宠物信息失败，请刷新页面重试');
                        }
                    }
                } else {
                    console.log('API适配器未加载，等待外部数据传入...');
                }
            } catch (error) {
                console.error('页面初始化失败:', error);
                if (window.loadingManager) {
                    window.loadingManager.showError('页面初始化失败，请刷新页面重试');
                    // 失败时也恢复抑制，避免影响后续操作
                    if (typeof window.loadingManager.unsuppress === 'function') {
                        // window.loadingManager.unsuppress('正在获取宠物信息');
                    }
                }
            }
        }

        /**
         * 创建父窗口函数的降级处理
         * 在浏览器环境中提供这些函数的替代实现
         */
        function createParentWindowFallbacks() {
            if (!window.parent || window.parent === window) {
                // 在浏览器环境中，创建父窗口函数的替代实现
                window.parent = window.parent || {};

                // 消息显示函数
                window.parent.showBox = function(message) {
                    if (window.loadingManager) {
                        // 根据消息内容判断类型
                        if (message.includes('成功')) {
                            window.loadingManager.showSuccess(message);
                        } else if (message.includes('失败') || message.includes('错误')) {
                            window.loadingManager.showError(message);
                        } else {
                            window.loadingManager.showInfo(message);
                        }
                    } else {
                        // 降级到原生alert
                        alert(message);
                    }
                };

                // 装备悬停提示函数
                window.parent.hoveZB_1 = function(event, element, color, id, name, type, petId) {
                    // 在浏览器中显示简单的tooltip
                    if (name && name !== 'undefined') {
                        element.title = `${type}: ${name} (ID: ${id})`;
                    }
                };

                // 隐藏悬停提示函数
                window.parent.hoveWp1hide = function() {
                    // 清除所有tooltip
                    document.querySelectorAll('[title]').forEach(el => {
                        if (el.title.includes('ID:')) {
                            el.title = '';
                        }
                    });
                };

                // 页面加载完成回调
                window.parent.loadOK = function() {
                    console.log('页面加载完成');
                };

                console.log('已创建父窗口函数的浏览器降级实现');
            }
        }

        /**
         * 获取当前用户ID的辅助函数
         * @returns {number} 用户ID
         */
        function getCurrentUserId() {
            // 优先从认证管理器获取
            if (window.authManager && window.authManager.isLoggedIn()) {
                const userId = window.authManager.getCurrentUserId();
                if (userId) {
                    console.log(`✅ 从认证管理器获取用户ID: ${userId}`);
                    return userId;
                }
            }

            // 备用方案：从全局函数获取
            if (typeof window.getCurrentUserId === 'function') {
                const userId = window.getCurrentUserId();
                if (userId && userId !== 1) {
                    console.log(`✅ 从全局函数获取用户ID: ${userId}`);
                    return userId;
                }
            }

            // 兼容性：从父窗口获取
            if (window.parent && window.parent.userId) {
                console.log(`✅ 从父窗口获取用户ID: ${window.parent.userId}`);
                return window.parent.userId;
            }

            // 兼容性：从当前窗口获取
            if (window.userId) {
                console.log(`✅ 从当前窗口获取用户ID: ${window.userId}`);
                return window.userId;
            }

            // 兼容性：从URL参数获取
            const urlParams = new URLSearchParams(window.location.search);
            const userIdFromUrl = urlParams.get('userId');
            if (userIdFromUrl) {
                const userId = parseInt(userIdFromUrl);
                console.log(`✅ 从URL参数获取用户ID: ${userId}`);
                return userId;
            }

            // 检查是否为开发环境
            const isDev = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.hostname.includes('dev');

            if (isDev) {
                console.warn('⚠️ 开发环境：未能获取到有效用户ID，使用测试用户ID: 1');
                return 1;
            } else {
                console.error('❌ 生产环境：用户未登录，无法获取用户ID');
                // 生产环境下跳转到登录页面
                if (typeof redirectToLogin === 'function') {
                    redirectToLogin();
                } else {
                    window.location.href = '/Login.html';
                }
                return null;
            }
        }

        /**
         * 获取当前用户信息的辅助函数
         * @returns {Object|null} 用户信息对象
         */
        function getCurrentUser() {
            // 优先从认证管理器获取
            if (window.authManager && window.authManager.isLoggedIn()) {
                const user = window.authManager.getCurrentUser();
                if (user) {
                    console.log(`✅ 从认证管理器获取用户信息:`, user);
                    return user;
                }
            }

            // 备用方案：构造基本用户信息
            const userId = getCurrentUserId();
            if (userId && userId !== 1) {
                return {
                    userId: userId,
                    nickname: `用户${userId}`,
                    username: `user${userId}`
                };
            }

            console.warn('⚠️ 未能获取到用户信息');
            return null;
        }

        /**
         * 获取当前主宠ID的辅助函数
         * @returns {number} 主宠ID
         */
        function getCurrentMainPetId() {
            // 从全局宠物数据中查找主宠（状态为0的宠物）
            if (allPetsData && allPetsData.length > 0) {
                const mainPet = allPetsData.find(pet => pet.状态 == "0");
                if (mainPet) {
                    return mainPet.宠物序号 || mainPet.id || mainPet.id;
                }
            }

            // 尝试从其他可能的位置获取
            if (window.parent && window.parent.currentMainPetId) {
                return window.parent.currentMainPetId;
            }
            if (window.currentMainPetId) {
                return window.currentMainPetId;
            }

            // 默认返回null
            return null;
        }

        /**
         * 调用GetPetAttributes接口获取宠物属性
         * @param {number} userId - 用户ID
         * @param {number} petId - 宠物ID
         */
        async function loadPetAttributes(userId, petId) {
            try {
                console.log('开始获取宠物属性 - 用户ID:', userId, '宠物ID:', petId);

                const response = await fetch('/api/Player/GetPetAttributes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        UserId: userId,
                        PetId: petId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('获取宠物属性成功:', result);

                if (result) {
                    updatePetAttributesDisplay(result);
                } else {
                    console.error('获取宠物属性失败: 返回数据为空');
                }
            } catch (error) {
                console.error('获取宠物属性失败:', error);
            }
        }

        /**
         * 更新宠物属性显示 - 统一的属性显示更新函数
         * @param {Object} attributes - 属性数据
         */
        function updatePetAttributesDisplay(attributes) {
            try {
                console.log('开始更新宠物属性显示:', attributes);

                // 更新基础属性 - 使用showInt格式化数字显示
                if (attributes.hp !== undefined) {
                    $('.最大生命').text(showInt(attributes.hp));
                    $('.生命').text(showInt(attributes.hp)); // 兼容旧字段名
                }
                if (attributes.mp !== undefined) {
                    $('.最大魔法').text(showInt(attributes.mp));
                    $('.魔法').text(showInt(attributes.mp)); // 兼容旧字段名
                }
                if (attributes.atk !== undefined) {
                    $('.攻击').text(showInt(attributes.atk));
                }
                if (attributes.def !== undefined) {
                    $('.防御').text(showInt(attributes.def));
                }
                if (attributes.hit !== undefined) {
                    $('.命中').text(showInt(attributes.hit));
                }
                if (attributes.dodge !== undefined) {
                    $('.闪避').text(showInt(attributes.dodge));
                }
                if (attributes.spd !== undefined) {
                    $('.速度').text(showInt(attributes.spd));
                }
                if (attributes.growth !== undefined) {
                    $('.成长').text(showInt(attributes.growth));
                }

                // 更新扩展属性 - 统一处理百分比属性
                const formatPercentage = (value) => {
                    if (value === undefined || value === null || isNaN(parseFloat(value))) {
                        return '0%';
                    }
                    return `${(parseFloat(value) * 100).toFixed(1)}%`;
                };

                // 更新加深、抵消、吸血、吸魔属性
                $('.加深').text(formatPercentage(attributes.deepen));
                $('.抵消').text(formatPercentage(attributes.offset));
                $('.吸血').text(formatPercentage(attributes.vamp));
                $('.吸魔').text(formatPercentage(attributes.vampMp));

                // 更新p2_right区域内容 - 保持原有结构
                const p2RightContent = [
                    '金抗：0<br>',
                    '木抗：0<br>',
                    '水抗：0<br>',
                    '火抗：0<br>',
                    '土抗：0<br>',
                    `加深：<span class="加深">${formatPercentage(attributes.deepen)}</span><br>`,
                    `抵消：<span class="抵消">${formatPercentage(attributes.offset)}</span><br>`,
                    `吸血：<span class="吸血">${formatPercentage(attributes.vamp)}</span><br>`,
                    `吸魔：<span class="吸魔">${formatPercentage(attributes.vampMp)}</span><br>`
                ];

                $('#p2_right').html(p2RightContent.join(''));

                console.log('宠物属性显示更新完成');
            } catch (error) {
                console.error('更新宠物属性显示失败:', error);
            }
        }

        $(function() {
            // DOM加载完成后的初始化
            console.log('DOM加载完成，开始页面初始化...');

            // 创建父窗口函数的降级处理
            createParentWindowFallbacks();

            $("#bottom div").width(66);
            $(".pettab li").click(function() {
                $("#page1").hide();
                $("#page2").hide();
                $("#page3").hide();
                $(".pettab li").removeClass("on");
                $(this).addClass("on");
                $("#page" + ($(this).index() + 1)).show();
            });

            // 延迟一小段时间确保所有脚本都加载完成后再初始化
            setTimeout(function() {
                initializePage();
            }, 500);

        });
        var showZi = false;

        /**
         * 宠物显示层级切换函数
         * 控制宠物图像的显示层级，可以将宠物置顶或置底
         * 用于解决宠物图像与其他UI元素的遮挡问题
         */
        function showZindex() {
            showZi = !showZi; // 切换显示状态
            if (showZi) {
                // 置顶：设置极高的z-index值
                $(".ppp1").css("z-index", "99999999");
                $(".ppp1").css("position", "POSITION");
            } else {
                // 置底：恢复正常的z-index值
                $(".ppp1").css("z-index", "9999");
                $(".ppp1").css("position", "none");
            }
            // 更新按钮文字
            $(".btn_zd").html(showZi ? "宠物置底" : "宠物置顶");
            // 提示用户可能的影响
            window.parent.showBox("宠物置顶可能会影响按钮的点击，如果有影响，请重新置底！");
        }

        /**
         * 数值格式化显示函数
         * 将大数值转换为易读的格式（如：亿、百亿、兆、百兆）
         * @param {number} i - 要格式化的数值
         * @returns {string} 格式化后的字符串
         */
        function showInt(i) {
            var num = i;
            if (i >= 100000000000000) {
                // 百兆级别 (100万亿以上)
                i = toDecimal(i / 100000000000000);
                num = i + "百兆";
            } else if (i >= 1000000000000) {
                // 兆级别 (1万亿以上)
                i = toDecimal(i / 1000000000000);
                num = i + "兆";
            } else if (i >= 10000000000) {
                // 百亿级别 (100亿以上)
                i = toDecimal(i / 10000000000);
                num = i + "百亿";
            } else if (i >= 100000000) {
                // 亿级别 (1亿以上)
                i = toDecimal(i / 100000000);
                num = i + "亿";
            }
            return num;
        }

        /**
         * 小数点保留函数
         * 将数值保留两位小数
         * @param {number} x - 要处理的数值
         * @returns {number} 保留两位小数的数值
         */
        function toDecimal(x) {
            var f = parseFloat(x);
            if (isNaN(f)) {
                return;
            }
            // 四舍五入保留两位小数
            f = Math.round(x * 100) / 100;
            return f;
        }
        var Mid;
        var 战斗力 = 0;
        var allPetsData = []; // 存储所有宠物数据的全局变量
        var allPetSkills = {}; // 存储所有宠物技能数据的全局变量 {petId: [skills]}

        /**
         * 更新主宠物信息显示函数
         * 根据传入的JSON数据更新页面上的宠物属性显示
         * 同时计算并更新战斗力数值
         * @param {Object} json - 包含宠物所有属性信息的JSON对象
         */
        function updateMainPet(json) {

            console.log('更新主宠物信息:', json);
            
            // 安全检查：确保传入的json对象不为空
            if (!json || typeof json !== 'object') {
                console.error('updateMainPet: 传入的json对象无效', json);
                return;
            }

            // 如果有指定形象，使用指定形象覆盖默认形象
            if (json.指定形象 != null && json.指定形象 != "") {
                json.形象 = json.指定形象;
            }
            Mid = json.宠物序号; // 设置当前宠物ID

            // 只更新基本信息，不重复处理属性
            $(".等级").text(json.等级);
            $(".五行").text(json.五行);
            $(".境界").text(json.境界);
            $(".进化次数").text(json.已进化次数);

            // 计算战斗力（保留原有逻辑）
            战斗力 = 0;
            战斗力 += (json.生命 || json.最大生命 || 0) / 10000;
            战斗力 += (json.魔法 || json.最大魔法 || 0) / 10000;
            战斗力 += (json.攻击 || 0) / 800;
            战斗力 += (json.防御 || 0) / 1000;
            战斗力 += (json.命中 || 0) / 500;
            战斗力 += (json.闪避 || 0) / 4000;
            战斗力 += (json.速度 || 0) / 2500;

            // 更新技能显示 - 支持新的技能数据格式
            $(".技能列表").html("");

            // 优先使用新的技能列表格式
            if (json.技能列表 && Array.isArray(json.技能列表) && json.技能列表.length > 0) {
                console.log('使用新的技能列表格式:', json.技能列表);

                for (var i = 0; i < json.技能列表.length; i++) {
                    var skill = json.技能列表[i];

                    // 正确映射字段名称（注意大小写）
                    var skillId = skill.skillId || "";
                    var skillName = (skill.skillConfig && skill.skillConfig.skillName) || "未知技能";
                    var skillLevel = skill.skillLevel || 0;
                    var skillType = (skill.skillConfig && skill.skillConfig.skillType) || "ACTIVE";
                    var isPassive = skillType === "PASSIVE" || (skill.skillConfig && skill.skillConfig.isBuff);
                    var description = (skill.skillConfig && skill.skillConfig.buffInfo) || "";

                    var skillTypeDisplay = isPassive ? "[加成]" : "[技能]";
                    var skillTypeForClick = isPassive ? "BUFF" : "技能";

                    // 构建技能字符串（兼容原有格式）
                    var skillStr = skillName + "|" + skillLevel + "|" + skillId + "|" + skillType + "|" + isPassive + "|" + description;

                    console.log('处理技能:', {
                        原始数据: skill,
                        解析后: {
                            skillId: skillId,
                            skillName: skillName,
                            skillLevel: skillLevel,
                            skillType: skillType,
                            isPassive: isPassive
                        }
                    });

                    $(".技能列表").append(
                        "<li data-str='" + skillStr + "' data-id='" + skillId + "' onselectstart='return false;' onclick=\"sj('" + skillTypeForClick + "'," +
                        skillId + ",this," + Mid + ")\"><b>" + skillTypeDisplay + "</b> " + skillName +
                        "(<span class='jnlv'>" + skillLevel + "</span>)</span>&nbsp;<span style='color:#3c5dff;' onclick=\"deleteJN('" + Mid + "','" + skillId + "')\">删</span></li>");

                    // 计算战斗力
                    战斗力 += isPassive ? (skillLevel * 35) : (skillLevel * 15);
                }
            }
            // 降级处理：使用旧的技能显示格式
            else if (typeof(json.技能显示) != "undefined" && json.技能显示 !== "") {
                console.log('使用旧的技能显示格式:', json.技能显示);

                var 技能 = json.技能显示.split(",");
                for (var i = 0; i < 技能.length; i++) {
                    var 技能组 = 技能[i].split("|");
                    if (技能组.length >= 2) {
                        if (技能组[4] == "true") {
                            $(".技能列表").append(
                                "<li data-str='" + 技能[i] + "' data-id='" + 技能组[2] + "' onselectstart='return false;'  onclick=\"sj('BUFF'," +
                                技能组[2] + ",this," + Mid + ")\"><b>[加成]</b> " + 技能组[0] +
                                "(<span class='jnlv'>" + 技能组[1] + "</span>)</span>&nbsp;<span style='color:#3c5dff;' onclick=\"deleteJN('" + Mid + "','" + 技能组[2] + "')\">删</span></li>");
                            战斗力 += 技能组[1] * 35;
                        } else {
                            $(".技能列表").append(
                                "<li data-str='" + 技能[i] + "' data-id='" + 技能组[2] +
                                "' onselectstart='return false;' ><span onclick=\"sj('技能'," +
                                技能组[2] + ",this," + Mid + ")\"><b>[技能]</b> " + 技能组[0] + "(<span class='jnlv'>" +
                                技能组[1] + "</span>)</span>&nbsp;<span style='color:#3c5dff' onclick=\"deleteJN('" + Mid + "','" + 技能组[2] + "')\">删</span></li>");
                            战斗力 += 技能组[1] * 15;
                        }
                    }
                }
            } else {
                console.log('该宠物没有技能');
                $(".技能列表").append("<li style='color:#999;'>该宠物暂无技能</li>");
            }

            // 技能悬停提示事件
            $(".技能列表 li").mousemove(function(e) {
                e = e || window.event;
                $(".skillTips").show();
                var 技能组 = $(this).attr("data-str").split("|");

                $(".s_name").html(技能组[0]);
                $(".s_value").html(技能组[5] || "暂无描述");
                var stop = e.pageY + 15;

                var sleft = (e.pageX + 22);
                if (sleft >= 725) {
                    sleft = 725;
                }
                if (stop >= 250) {
                    stop = 250;
                }
                $(".s_type").html(技能组[4] == "true" ? "BUFF" : "主动");
                $(".skillTips").css("top", stop + "px");
                $(".skillTips").css("left", sleft + "px");
            });
            $(".技能列表 li").mouseout(function(e) {
                $(".skillTips").hide();
            });

            形象 = json.形象;

                wuxing = json.五行;
                if ((wuxing == "聖" || wuxing == "佛" || wuxing == "萌" || wuxing == "灵" || wuxing == "次元" || wuxing == "巫") && 形象.indexOf("8023") == -1) {

                    showFlashPet("z");
                    if(形象 < 503 && 形象 != 1000){
                        setPetICO(json.形象);
                    }else{
                        if (png_) {
                        $(".petO").html("");
                        for (var i = 0; i <= zCount; i++) {

                            $(".petO").prepend("<img class='yingbi z" + i + "' src='Content/PetPhoto/z" + 形象 + "_" + i + ".png'>");

                        }
                        for (var i = 0; i <= sCount; i++) {
                            $(".petO").prepend("<img class='yingbi s" + i + "' src='Content/PetPhoto/s" + 形象 + "_" + i + ".png'>");

                        }
                    }
                    }


                } else {
                    setPetICO(json.形象);
                }

            战斗力 = Math.ceil(战斗力);
            $(".战斗力").text(战斗力);

            // 调用GetPetAttributes接口获取精确的宠物属性
            const userId = getCurrentUserId();
            const petId = json.宠物序号 || json.id || json.id;
            if (userId && petId) {
                console.log('调用GetPetAttributes接口 - 用户ID:', userId, '宠物ID:', petId);
                loadPetAttributes(userId, petId);
            } else {
                console.warn('无法调用GetPetAttributes接口 - 缺少用户ID或宠物ID', { userId, petId });
            }
        }

        /**
         * 删除宠物技能函数（异步）
         * 提供用户友好的确认对话框，确认后调用API删除指定技能
         * 支持新的确认对话框组件和传统confirm方式的降级处理
         * @param {string|number} pet - 宠物ID
         * @param {string|number} skill - 技能ID
         */
        async function deleteJN(pet, skill) {
            // 显示确认对话框，优先使用新的UI组件
            const confirmed = window.confirmDialog ?
                await window.confirmDialog.show({
                    title: '确认删除技能',
                    message: '您确定要删除该技能吗？本操作不可逆！',
                    type: 'danger',
                    confirmText: '删除',
                    cancelText: '取消'
                }) :
                confirm("您确定要删除该技能吗？本操作不可逆！");

            if (confirmed) {
                if (typeof window.external.forgetSkill !== 'undefined') {
                    try {
                        const result = await window.external.forgetSkill(pet, skill);
                        if (result.success) {
                            if (window.loadingManager) {
                                window.loadingManager.showSuccess("技能删除成功！");
                            } else {
                                window.parent.showBox("技能删除成功！");
                            }
                            // 刷新页面数据
                            if (typeof window.external.updatePetInfo_page !== 'undefined') {
                                window.external.updatePetInfo_page();
                            }
                        } else {
                            const errorMsg = result.message || "技能删除失败，请重试！";
                            if (window.loadingManager) {
                                window.loadingManager.showError(errorMsg);
                            } else {
                                window.parent.showBox(errorMsg);
                            }
                        }
                    } catch (error) {
                        console.error('删除技能失败:', error);
                        const errorMsg = "系统错误，请重试！";
                        if (window.loadingManager) {
                            window.loadingManager.showError(errorMsg);
                        } else {
                            window.parent.showBox(errorMsg);
                        }
                    }
                } else {
                    // 降级到原有方法
                    var result = window.external.deleteJN(pet, skill);
                    if (result == 1) {
                        if (window.loadingManager) {
                            window.loadingManager.showSuccess("技能删除成功！");
                        } else {
                            window.parent.showBox("技能删除成功！");
                        }
                    } else {
                        if (window.loadingManager) {
                            window.loadingManager.showError("技能删除失败，请重试！");
                        } else {
                            window.parent.showBox("技能删除失败，请重试！");
                        }
                    }
                }
            }
        }


        /**
         * 技能升级函数（异步）
         * 调用API升级指定宠物的指定技能，并更新页面显示
         * 支持新的消息提示组件和传统提示方式的降级处理
         * @param {string} type - 技能类型（如'技能'、'BUFF'等）
         * @param {string|number} id - 技能ID
         * @param {jQuery} dom - 技能对应的DOM元素，用于更新等级显示
         * @param {string|number} petid - 宠物ID
         */
        async function sj(type, id, dom, petid) {
            if (typeof window.external.upgradeSkill !== 'undefined') {
                try {
                    const result = await window.external.upgradeSkill(petid, id);
                    if (result.success) {
                        if (window.loadingManager) {
                            window.loadingManager.showSuccess("技能升级成功！");
                        } else {
                            window.parent.showBox("技能升级成功！");
                        }
                        if (result.upgradedSkill && result.upgradedSkill.SkillLevel) {
                            $(dom).find(".jnlv").html(result.upgradedSkill.SkillLevel);
                        }
                        // 刷新页面数据
                        if (typeof window.external.updatePetInfo_page !== 'undefined') {
                            window.external.updatePetInfo_page();
                        }
                    } else {
                        let errorMsg;
                        if (result.message && result.message.includes("最高等级")) {
                            errorMsg = "技能已经满级啦~~";
                        } else {
                            errorMsg = result.message || "技能升级失败！请确定您拥有升级道具！";
                        }

                        if (window.loadingManager) {
                            if (result.message && result.message.includes("最高等级")) {
                                window.loadingManager.showWarning(errorMsg);
                            } else {
                                window.loadingManager.showError(errorMsg);
                            }
                        } else {
                            window.parent.showBox(errorMsg);
                        }
                    }
                } catch (error) {
                    console.error('技能升级失败:', error);
                    const errorMsg = "系统错误，请重试！";
                    if (window.loadingManager) {
                        window.loadingManager.showError(errorMsg);
                    } else {
                        window.parent.showBox(errorMsg);
                    }
                }
            } else {
                // 降级到原有方法
                var returns = window.external.jnsj(petid, id, type);
                if (returns > -1) {
                    window.parent.showBox("技能升级成功！");
                    $(dom).find(".jnlv").html(returns);
                } else if (returns == -2) {
                    if (window.loadingManager) {
                        window.loadingManager.showWarning("技能已经满级啦~~");
                    } else {
                        window.parent.showBox("技能已经满级啦~~");
                    }
                } else {
                    if (window.loadingManager) {
                        window.loadingManager.showError("技能升级失败！请确定您拥有升级道具！");
                    } else {
                        window.parent.showBox("技能升级失败！请确定您拥有升级道具！");
                    }
                }
            }
        }

        /**
         * JSON字符串转换函数
         * 将对象转换为JSON字符串格式
         * @param {Object} i - 要转换的对象
         * @returns {string} JSON字符串
         */
        function getJSONStr(i) {
            return JSON.stringify(i);
        }
        var 形象; // 全局变量：当前宠物形象ID

        /**
         * 读取宠物列表数据函数
         * 解析宠物列表JSON数据，更新主宠物信息和宠物列表显示
         * @param {string} json - 包含宠物列表信息的JSON字符串
         */
        function readPet(json) {
            var j = $.parseJSON(json); // 解析JSON数据
            allPetsData = j; // 保存所有宠物数据到全局变量
            $("#bottom").html(""); // 清空宠物列表容器
            var ii = 0; // 宠物计数器
            var html;
            var i;
            console.log("宠物列表：", j);

            // 遍历所有宠物数据
            for (i = 0; i < j.length; i++) {
                var c = "yy"; // 默认样式类
                ii++;

                // 如果是主战宠物（状态为0）
                if (j[i].状态 == "0") {
                    // 更新主宠物信息显示
                    updateMainPet(j[i]);


                    c = "";
                }
                html = "<div  class=\"" + c + "\" onclick=\"handlePetClick(" + j[i].宠物序号 + ",$(this).index())\">" +
                    "<em>" +
                    "<a style='' >" + j[i].宠物名字 + "<br />LV " + j[i].等级 + "</a>" +
                    "</em>" +
                    "<img style=\"border-radius: 5px; \" src=\"Content/PetPhoto/k" + j[i].形象 + ".gif\" onerror=\"this.src='Content/PetPhoto/k" + j[i].形象 + ".png'\" />" +
                    "</div>";


                $("#bottom").append(html);

            }
            html = "<div><em></em><img style=\"border-radius: 5px; \" src=\"Content/PetPhoto/nopet.gif\" /></div>";
            for (i = 0; i < 3 - ii; i++) {

                $("#bottom").append(html);

            }

        }



        /**
         * 页面初始化函数
         * 按顺序初始化宠物信息、装备信息、用户信息等页面数据
         * 使用setTimeout确保数据加载的顺序性
         * @param {string} pet - 宠物数据JSON字符串
         * @param {string} zb - 装备数据JSON字符串
         * @param {string} user - 用户数据JSON字符串
         */
        function chushihua(pet, zb, user) {
            // 延迟1ms加载装备信息，确保DOM准备就绪
            setTimeout(function() {
                loadZB(zb);
            }, 1)
            // 延迟1ms加载用户信息
            setTimeout(function() {
                showUserInfo(user);
            }, 1)
            // 延迟1ms加载宠物信息
            setTimeout(function() {
                readPet(pet);
            }, 1)
            // 通知父窗口加载完成
            window.parent.loadOK();
        }

        /**
         * 设置宠物头像函数
         * 根据宠物ID设置对应的宠物头像图片
         * @param {number} id - 宠物形象ID
         */
        function setPetICO(id) {
            var html;

            // 检查ID是否有效
            if (id === undefined || id === null || id === '' || isNaN(id)) {
                console.warn('setPetICO: 无效的宠物形象ID:', id, '使用默认形象264');
                id = 264; // 使用默认宠物形象
            }

            // 确保ID是数字类型
            id = parseInt(id);

            if (id >= 1000) {
                // ID大于等于1000的宠物使用空白图片（可能是特殊宠物）
                html = " <IMG STYLE=\" height: 150px;width: 166px;\" />";
            } else {
                // 普通宠物使用对应的GIF图片
                html = " <IMG src=\"Content/PetPhoto/z" + id + ".gif\" onerror=\"this.src='Content/PetPhoto/z264.gif'\" />";
            }
            // 更新宠物头像容器
            $(".petO").html(html);
        }
        var Init = 0;
        var ssTime = 100;

        /**
         * 显示PNG格式宠物动画函数
         * 启动宠物的PNG序列帧动画播放
         * @param {string} t - 动画类型标识（如'z'表示站立动画）
         */
        function showPNG(t) {
            // 显示第一帧动画
            $(".z1").removeClass("yingbi").addClass("petPng");

            // 清除之前的动画定时器
            if (Init != 0) clearInterval(Init);

            // 设置动画播放速度
            ssTime = 100; // 默认100ms间隔
            if (形象 == 628) ssTime = 50; // 特定宠物使用更快的动画速度

            // 启动动画播放定时器
            Init = setInterval("play" + t + "()", ssTime);
        }

        var zI = 1; // 当前动画帧索引

        /**
         * 播放站立动画函数
         * 循环播放宠物的站立动画序列帧
         * 通过切换CSS类来显示/隐藏不同的动画帧
         */
        function playz() {
            zI++; // 切换到下一帧

            if (zI > zCount) {
                // 如果超过最大帧数，隐藏当前帧并重置到第一帧
                $(".z" + (zI - 1)).addClass("yingbi").removeClass("petPng");
                zI = 1;
            } else {
                // 隐藏上一帧
                $(".z" + (zI - 1)).addClass("yingbi").removeClass("petPng");
            }

            // 显示当前帧
            $(".z" + zI).removeClass("yingbi").addClass("petPng");
        }
        var zCount = 10;
        var sCount = 31;
        var png_ = true;

        /**
         * 显示Flash格式宠物动画函数
         * 根据宠物形象ID决定使用PNG序列帧动画还是Flash动画
         * @param {string} t - 动画类型标识
         */
        function showFlashPet(t) {
            // 检查是否有PNG动画配置
            var petl = PngPet.split('|');
            for (var i = 0; i < petl.length; i++) {
                var p = petl[i].split(',');
                if (形象 == p[0]) {
                    // 如果找到PNG配置，设置动画帧数并使用PNG动画
                    zCount = p[1]; // 站立动画帧数
                    sCount = p[2]; // 技能动画帧数
                    showPNG(t);
                    return;
                }
            }

            // 如果没有PNG配置，使用Flash动画
            var width = 488; // 默认宽度
            if (形象 > 513) {
                // 新版宠物使用不同的前缀和尺寸
                t = "f";
                width = 232;
            }
            png_ = true;

            // 生成Flash嵌入代码
            var fhtml = "<div><object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' id='sw' width='" + width + "' height='200' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab'>" +
                "<param name='movie' value='Content/FlashPet/" + t + 形象 + ".swf'>" +
                "<param name='allowScriptAccess' value='always' />  " +
                "<param name='allowFullScreen' value='false' />  " +
                "<param name='quality' value='high' />  " +
                "<param name='wmode' value='transparent' />" +
                "<embed src='Content/FlashPet/" + t + 形象 + ".swf' quality='high' width='488' height='200' name='sw' align='middle' play='true' loop='false' allowScriptAccess='always' type='application/x-shockwave-flash' wmode='transparent' pluginspage='http://www.macromedia.com/go/getflashplayer'></object></div>";

            // 更新宠物显示容器
            $(".petO").html(fhtml);
        }

        /**
         * 装备穿戴函数
         * 验证装备是否可以穿戴，如果可以则移除装备项并重置装备槽显示
         * @param {string|number} i - 装备ID
         * @param {jQuery} t - 装备对应的DOM元素
         * @param {string} s - 装备槽位标识
         */
        function t(i, t, s) {
            // 调用AJAX验证装备是否可以穿戴
            if (ajax.vailZb(i).value == "ok") {
                // 移除装备项
                $(t).remove();

                // 重置各装备槽位的显示文字（如果为空）
                if ($("#zbst").html() == "") {
                    $("#zbst").html("身体");
                }
                if ($("#zbwq").html() == "") {
                    $("#zbwq").html("武器");
                }
                if ($("#zbjb").html() == "") {
                    $("#zbjb").html("脚部");
                }
                if ($("#zbcb").html() == "") {
                    $("#zbcb").html("翅膀");
                }
                if ($("#zbbs").html() == "") {
                    $("#zbbs").html("宝石");
                }
                if ($("#zbdj").html() == "") {
                    $("#zbdj").html("道具");
                }
                if ($("#zbjz").html() == "") {
                    $("#zbjz").html("戒指");
                }
                if ($("#zbsz").html() == "") {
                    $("#zbsz").html("手镯");
                }
                if ($("#zbxl").html() == "") {
                    $("#zbxl").html("项链");
                }
                if ($("#zbtb").html() == "") {
                    $("#zbtb").html("头部");
                }
                if ($("#zbls").html() == "") {
                    $("#zbls").html("灵饰");
                }
                if ($("#zbfb").html() == "") {
                    $("#zbfb").html("法宝");
                }



            } else {
                parent.window.showBox("系统繁忙！");
            }
        }
        var pid;

        /**
         * 处理宠物点击事件的包装函数
         * 用于处理异步的宠物切换操作
         * @param {string|number} id - 宠物ID
         * @param {number} index - 宠物在列表中的索引位置
         */
        function handlePetClick(id, index) {
            // 调用异步函数，但不等待结果（避免阻塞UI）
            setPetWz(id, index).catch(error => {
                console.error('宠物切换失败:', error);
                if (window.loadingManager) {
                    window.loadingManager.showError('宠物切换失败，请重试');
                } else {
                    window.parent.showBox('宠物切换失败，请重试');
                }
            });
        }

        /**
         * 设置宠物位置/切换主战宠物函数
         * 切换当前显示的宠物，同时设置为主战宠物，更新宠物列表的选中状态和宠物信息
         * @param {string|number} id - 宠物ID
         * @param {number} index - 宠物在列表中的索引位置
         */
        async function setPetWz(id, index) {
            pid = id; // 设置当前宠物ID

            console.log(`开始切换宠物，ID: ${id}, 索引: ${index}`);

            // 先调用API设置主战宠物
            let switchSuccess = false;
            if (typeof window.external.switchPet === 'function') {
                try {
                    console.log('调用API设置主战宠物...');
                    switchSuccess = await window.external.switchPet(id);

                    if (switchSuccess) {
                        console.log('主战宠物设置成功');
                        // 静默成功，不显示提示框
                    } else {
                        console.log('主战宠物设置失败');
                        // 只在控制台记录错误，不显示弹窗
                        return; // 如果设置主战宠物失败，不继续执行
                    }
                } catch (error) {
                    console.error('设置主战宠物时发生错误:', error);
                    // 只在控制台记录错误，不显示弹窗
                    return;
                }
            } else {
                console.log('switchPet API不可用，仅更新显示');
                // 在没有API的情况下，仍然更新显示
                switchSuccess = true;
            }

            // 如果设置主战宠物成功，更新页面显示
            if (switchSuccess) {
                // 更新宠物列表的选中状态
                var dom = $("#bottom div");
                for (var i = 0; i < dom.length; i++) {
                    // 如果不是当前选中的宠物且有图片，添加灰色样式
                    if ($(dom[i]).find("img").attr("src") != undefined && $(dom[i]).index() != $("#bottom div:eq(" + index + ")").index()) {
                        $(dom[i]).addClass("yy"); // 添加非选中样式
                    } else {
                        $(dom[i]).removeClass("yy"); // 移除非选中样式
                    }
                }

                // 重置背饰相关状态
                bsid = -1;
                $(".touming").removeClass("touming");
                $(".i15").css("background", "url()");

                // 更新主宠物信息显示 - 避免重复调用API
                // 如果switchPet API调用成功，说明主宠物信息已经通过API更新了，无需再次更新
                if (!switchSuccess) {
                    console.log('API切换失败，使用本地数据更新显示');
                    try {
                        // 从本地数据中查找指定宠物
                        const selectedPet = allPetsData.find(pet => pet.宠物序号 == id);

                        if (selectedPet) {
                            console.log('从本地数据获取宠物信息:', selectedPet);
                            updateMainPet(selectedPet);
                        } else {
                            console.error('在本地数据中未找到宠物ID:', id);
                            console.log('当前所有宠物数据:', allPetsData);

                            // 降级处理：如果本地数据中没有找到，尝试刷新页面数据
                            if (typeof window.external.updatePetInfo_page === 'function') {
                                console.log('尝试刷新页面数据...');
                                window.external.updatePetInfo_page();
                            }
                        }
                    } catch (error) {
                        console.error('使用本地宠物数据失败:', error);

                        // 降级处理：如果本地数据处理失败，尝试刷新页面数据
                        if (typeof window.external.updatePetInfo_page === 'function') {
                            console.log('降级处理：尝试刷新页面数据...');
                            window.external.updatePetInfo_page();
                        }
                    }
                } else {
                    console.log('API切换成功，主宠物信息已通过API更新，跳过本地数据更新');
                }

                // 重置父窗口的技能相关变量
                window.parent.jid = null;
                window.parent.jname = "普通攻击";

                console.log('宠物切换完成');
            }
        }

        /**
         * 显示用户信息函数
         * 解析并显示用户的基本信息（名字、金币、水晶等）
         * @param {string} json - 包含用户信息的JSON字符串
         */
        function showUserInfo(json) {
            var j = $.parseJSON(json); // 解析JSON数据
            // 更新页面上的用户信息显示
            $(".玩家名字").text(j.名字);
            $(".金币").text(j.金币);
            $(".水晶").text(j.水晶);
            $(".元宝").text(j.元宝);
            $(".主宠名字").text(j.主宠名字);
            $(".宠物数量").text(j.宠物数量);
            $(".性别").text(j.sex);
        }

        var ddti = 0; // 背饰动画帧计数器
        var ddtTime = 0; // 背饰动画定时器

        /**
         * 显示背饰动画函数
         * 播放背饰的动画效果，通过改变背景位置实现帧动画
         * @param {string} name - 背饰名称，用于构建图片路径
         */
        function showDTBS(name) {
            clearInterval(ddtTime); // 清除之前的动画定时器
            ddti = 0; // 重置帧计数器

            // 设置背饰背景图片
            $(".i15").css("background", "url(content/img/prop/prop/ddt/" + name + ".png)")
            $(".tsBox").addClass("touming"); // 添加透明效果

            // 启动背饰动画定时器
            var ddtTime = setInterval(function() {
                ddti++; // 增加帧计数
                // 改变背景位置实现动画效果
                $(".i15").css("background-position-y", (250 * ddti) + "px")
                if (ddti >= 100000) ddti = 0; // 防止计数器溢出
            }, 100)
        }

        var bsname = ''; // 背饰名称
        var bsid = -1; // 背饰ID

        /**
         * 加载装备信息函数
         * 解析装备JSON数据并更新页面上的装备显示
         * @param {string} j - 包含装备信息的JSON字符串
         */
        function loadZB(j) {
            // 重置背饰相关变量
            bsname = '';
            bsid = -1;

            // 重置所有装备槽位显示文字
            $(".身体").html("身体");

            $(".武器").html("武器");

            $(".脚部").html("脚部");

            $(".翅膀").html("翅膀");

            $(".宝石").html("宝石");

            $(".道具").html("道具");

            $(".戒指").html("戒指");

            $(".手镯").html("手镯");

            $(".项链").html("项链");

            $(".头部").html("头部");
            $(".卡牌左").html("<img src=\"Content/Img/PetInfo/zbsx.gif\">");
            $(".卡牌右").html("<img src=\"Content/Img/PetInfo/zbsx.gif\">");
            $(".灵饰").html("灵饰");
            $(".法宝").html("法宝");

            // 处理装备数据 - 支持新旧格式
            let equipmentData = [];
            if (typeof j === 'string') {
                try {
                    equipmentData = $.parseJSON(j);
                } catch (error) {
                    console.error('解析装备JSON失败:', error);
                    return;
                }
            } else if (Array.isArray(j)) {
                equipmentData = j;
            } else {
                console.log('装备数据为空或格式不正确:', j);
                return;
            }

            console.log("装备数据：", equipmentData);

            for (var i = 0; i < equipmentData.length; i++) {
                var equipment = equipmentData[i];
                if (equipment == null) continue;

                // 兼容新旧数据格式
                var equipType = equipment.类型 || equipment.equipType || equipment.type || equipment.typeName || "";
                var equipId = equipment.ID || equipment.id || equipment.userEquipmentId || "";
                var equipName = equipment.Name || equipment.name || equipment.equipmentName || equipment.equipName || "";
                var equipIcon = equipment.ICO || equipment.icon || equipment.iconPath || "";

                console.log('装备处理:', {
                    equipType: equipType,
                    equipId: equipId,
                    equipName: equipName,
                    equipIcon: equipIcon,
                    equipment: equipment
                });

                if (!equipType || !equipIcon) {
                    console.log('跳过装备，缺少类型或图标:', { equipType, equipIcon });
                    continue;
                }

                var hz = "gif";
                if (equipIcon.indexOf("444444") != -1) {
                    hz = "png";
                }
                var style = "";
                if (equipType == "背饰") {
                    hz = "png"
                    bsname = equipName;
                    bsid = equipId;
                    $(".ppp1").css("cursor", "pointer");
                } else {
                    style = "margin-top:6px;";
                    $(".ppp1").css("cursor", "auto");
                }
                if (equipIcon.indexOf("ddt") != -1) {
                    showDTBS(equipIcon);
                } else {
                    console.log('设置装备到槽位:', {
                        selector: "." + equipType,
                        imagePath: "Content/Img/prop/prop/" + equipIcon + "." + hz,
                        equipId: equipId,
                        equipName: equipName
                    });
                    $("." + equipType).html("<img style='" + style + "' src=\"Content/Img/prop/prop/" + equipIcon + "." + hz + "\"/><div class='wpid' style='display:none'>" + equipId + "</div><div class='propName' style='display:none'>" + equipName + "</div>");
                }
            }
            $(".装备槽 li").mouseover(function(e) {
                e = e || window.event;
                window.parent.hoveZB_1(e, this, "#FFF", $(this).find(".wpid").html(), $(this).find(".propName").html(), "装备", Mid);
            });
            $(".装备槽 li").mouseout(function(e) {
                window.parent.hoveWp1hide();
            });
            $(".装备槽 li").dblclick(async function(e) {
                const equipmentId = $(this).find(".wpid").html();
                if (equipmentId && typeof window.external.unequipItem !== 'undefined') {
                    try {
                        const result = await window.external.unequipItem(equipmentId);
                        if (result.Success) {
                            if (window.loadingManager) {
                                window.loadingManager.showSuccess("装备卸下成功！");
                            } else {
                                window.parent.showBox("卸下装备成功！");
                            }
                            $(this).html("");
                            // 刷新装备显示
                            if (typeof window.external.updatePetInfo_page !== 'undefined') {
                                window.external.updatePetInfo_page();
                            }
                        } else {
                            const errorMsg = result.Message || "卸下装备失败！";
                            if (window.loadingManager) {
                                window.loadingManager.showError(errorMsg);
                            } else {
                                window.parent.showBox(errorMsg);
                            }
                        }
                    } catch (error) {
                        console.error('卸下装备失败:', error);
                        const errorMsg = "系统错误，请重试！";
                        if (window.loadingManager) {
                            window.loadingManager.showError(errorMsg);
                        } else {
                            window.parent.showBox(errorMsg);
                        }
                    }
                } else {
                    // 降级到原有方法
                    if (window.external.xiexia && window.external.xiexia(equipmentId)) {
                        window.parent.showBox("卸下装备成功！");
                        $(this).html("");
                    }
                }
            });
            $(".ppp1").mouseover(function(e) {
                e = e || window.event;
                if (bsid == -1) return;
                window.parent.hoveZB_1(e, this, "#FFF", bsid, bsname, "装备", Mid);
            });
            $(".ppp1").mouseout(function(e) {
                window.parent.hoveWp1hide();
            });
            $(".ppp1").dblclick(async function(e) {
                if (bsid == -1) return;

                if (typeof window.external.unequipItem !== 'undefined') {
                    try {
                        const result = await window.external.unequipItem(bsid);
                        if (result.Success) {
                            if (window.loadingManager) {
                                window.loadingManager.showSuccess("背饰卸下成功！");
                            } else {
                                window.parent.showBox("卸下背饰成功！");
                            }
                            bsid = -1;
                            $(".touming").removeClass("touming");
                            $(".i15").css("background", "url()");
                            // 刷新装备显示
                            if (typeof window.external.updatePetInfo_page !== 'undefined') {
                                window.external.updatePetInfo_page();
                            }
                        } else {
                            const errorMsg = result.Message || "卸下背饰失败！";
                            if (window.loadingManager) {
                                window.loadingManager.showError(errorMsg);
                            } else {
                                window.parent.showBox(errorMsg);
                            }
                        }
                    } catch (error) {
                        console.error('卸下背饰失败:', error);
                        const errorMsg = "系统错误，请重试！";
                        if (window.loadingManager) {
                            window.loadingManager.showError(errorMsg);
                        } else {
                            window.parent.showBox(errorMsg);
                        }
                    }
                } else {
                    // 降级到原有方法
                    if (window.external.xiexia && window.external.xiexia(bsid)) {
                        window.parent.showBox("卸下背饰成功！");
                        bsid = -1;
                        $(".touming").removeClass("touming");
                        $(".i15").css("background", "url()");
                    }
                }
            });
        }
    </script>
    <style type="text/css">
        #pageZb {
            display: none;
            position: absolute;
            overflow: hidden;
            padding: 0;
            width: auto;
            left: 0;
            top: 0;
            z-index: 1010;
            background: #1F1F30;
            filter: Alpha(opacity=90);
        }

        .skillTips {
            position: absolute;
            left: 642px;
            top: 28px;
            z-index: 999999;
            padding: 5px;
            background-image: url(content/img/petinfo/b.png);
            color: #fff;
            border-radius: 7px;
            display: none;
        }

        .s_name {
            font-weight: bold;
        }

        .s_type {
            margin-top: 4px;
        }
    </style>
</head>

<body onload="loadIF();" style="width:788px;">
    <div class="skillTips">
        <div class="s_name">次元复苏</div>
        <div class="s_type">BUFF</div>
        <div class="s_value">+40%攻击</div>
    </div>
    <div id="baginfo" style="margin-top: 0; z-index: 10020; position: absolute; display: none;">
        <table style="font-size:12px;" width="185" cellpadding="0" cellspacing="0" border="0">
            <tbody>
                <tr>
                    <td background="Content/Img/prop/border4_tl.gif" width="5" height="5"></td>
                    <td background="Content/Img/prop/border4_t.gif"></td>
                    <td background="Content/Img/prop/border4_tr.gif"></td>
                </tr>
                <tr>
                    <td width="5" background="Content/Img/prop/border4_l.gif"></td>
                    <td style="background:#1F1F30;filter:Alpha(opacity=90);" align="center"></td>
                    <td width="5" background="Content/Img/prop/border4_r.gif"></td>
                </tr>

                <tr>
                    <td width="5" background="Content/Img/prop/border4_l.gif"></td>
                    <td style="background:#1F1F30;filter:Alpha(opacity=90);">
                        <font color="#FEFDFA"><b><span id="bz_wpmc"></span></b></font><br>
                        <font color="#FEFDFA"><span id="bz_wpxx"></span></font><br>
                    </td>
                    <td width="5" background="Content/Img/prop/border4_r.gif"></td>
                </tr>
                <tr>
                    <td background="Content/Img/prop/border4_bl.gif" width="5" height="5"></td>
                    <td background="Content/Img/prop/border4_b.gif"></td>
                    <td background="Content/Img/prop/border4_br.gif"></td>
                </tr>
            </tbody>

        </table>
    </div>
    <div id="main">
        <div id="left">
            <div id="left_left">

                <span class="玩家名字">canku</span><br>宝贝：
                <font color="green"><span class="主宠名字"></span></font><br> 玩家性别：
                <span class="性别">帅哥</span><br> 战斗力：
                <span class="战斗力">0</span><br> 宠物数量：
                <span class="宠物数量"></span><br> 金币：
                <span class="金币">0</span><br> 元宝：
                <span class="元宝"></span><br> 水晶：
                <span class="水晶"></span><br>
            </div>
            <div id="left_right">
                <img src="Content/player/main.gif?rd=4847848">

            </div>
            <div id="bottom" style=" word-wrap: break-word;">

            </div>

        </div>
        <div id="right">
            <ul class="pettab" style="position:absolute;z-index:999">
                <li id="pet1" class="on">
                    <p class="p1">带领的宠物</p>
                </li>
                <li id="pet2" class="">
                    <p class="p2">属性</p>
                </li>
                <li id="pet3">
                    <p class="p3">技能</p>
                </li>
            </ul>
            <div style="height:35px"></div>
            <div id="page1" style="display: block;">
                <div id="right_bottom" style="display:block">
                    <div id="rb_left">
                        <div id="rbl_left">
                            <ul class="装备槽">
                                <li class="i1 身体 tsBox">身体</li>
                                <li class="i2 头部 tsBox">头部</li>
                                <li class="i3 项链 tsBox">项链</li>
                                <li class="i4 武器 tsBox">武器</li>
                                <li class="i5 手镯 tsBox">手镯</li>
                                <li class="i6 脚部 tsBox">脚部</li>
                                <li class="i7 戒指 tsBox">戒指</li>
                                <li class="i8 翅膀 tsBox">翅膀</li>
                                <li class="i9 宝石 tsBox">宝石</li>
                                <li class="i10 道具 tsBox">道具</li>
                                <li class="i11 卡牌右"><img src="Content/Img/PetInfo/zbsx.gif"></li>
                                <li class="i12 卡牌左"><img src="Content/Img/PetInfo/zbsx.gif"></li>
                                <li class="i13 灵饰 tsBox">灵饰</li>
                                <li class="i14 法宝 tsBox">法宝</li>

                            </ul>

                            <div class="i15 背饰">

                            </div>
                            <h2 class="petO ppp1">

                            </h2>
                        </div>

                        <h2 style="    margin-top: 8px;"><a href="javascript:void(0)" style="color: #424242;margin-left: -1px;" class="btn_zd" onclick="showZindex();">宠物置顶</a><a></a></h2><a>

			        </a></div><a>

			        <div id="rb_right">
				        等级：<span class="等级">10</span><br>
				        五行：<span class="五行">圣</span><br>
				        境界：<span class="境界">元神初具</span><br>
				        生命：<span class="最大生命">1000</span><br>
				        魔法：<span class="最大魔法">2000</span><br>
				        攻击：<span class="攻击">68000</span><br>
				        防御：<span class="防御">4000</span><br>
				        命中：<span class="命中">6000</span><br>
				        闪避：<span class="闪避">7000</span><br>
				        速度：<span class="速度">5000</span><br>
				        成长：<span class="成长">50.5</span>
			        </div>
		        </a></div><a>
	        </a></div><a>


	        <div id="page2" style="display: none;">
		        <div id="p2_left" style="    margin: 0 0 0 30px;">
			        <h2 class="petO ppp2">




			        </h2>

			        <ul class="Pet_attributes" style="z-index:99999">
				        <li>
					        等级：<span class="等级">10</span><br>
					        五行：<span class="五行">圣</span><br>
							境界：<span class="境界">元神初具</span><br>
					        进化次数：<span class="进化次数">0</span>
				        </li>
				        <li>
					        生命：<span class="最大生命">1000</span><br>
					        魔法：<span class="最大魔法">2000</span><br>
					        攻击：<span class="攻击">68000</span><br>
					        防御：<span class="防御">4000</span>
				        </li>
				        <li>
					        命中：<span class="命中">6000</span><br>
					        闪避：<span class="闪避">7000</span><br>
					        速度：<span class="速度">5000</span><br>
					        成长：<span class="成长">50.5</span>
				        </li>
			        </ul>
		        </div>
		        <div id="p2_right">
			        金抗：0<br>
			        木抗：0<br>
			        水抗：0<br>
			        火抗：0<br>
			        土抗：0<br>
			        加深：<span class="加深">0%</span><br>
			        抵消：<span class="抵消">0%</span><br>
			        吸血：<span class="吸血">0%</span><br>
			        吸魔：<span class="吸魔">0%</span><br>
		        </div>
	        </div>


	        <div id="page3" style="display:none">
            <div id="p3_left">
                    <h2 class="petO ppp3">
                    </h2>
                    <!--<h3>
                        <select id="jlist" name="jlist">
                            <option value="0">包裹中没有技能书</option>
                        </select>
                        <input type="button" onclick="useSkillProp()" style="width:39px; height:17px; margin-top:2px; background:url(i_bnt.gif) left -34px; border:none; cursor:pointer" value="">
                    </h3>-->
                </div>

                <div id="p3_right" style="position:relative;z-index:9999">
                    <ul class="技能列表"></ul>
                </div>
            </div>
        </a></div><a>

    </a></div><a>

    <script>
        // 页面加载完成后的认证检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 PetInfo页面加载完成，检查认证状态...');

            // 检查认证管理器是否可用
            if (typeof window.authManager !== 'undefined') {
                console.log('✅ 认证管理器已加载');

                if (window.authManager.isLoggedIn()) {
                    const user = window.authManager.getCurrentUser();
                    console.log('✅ 用户已登录:', user);
                } else {
                    console.warn('⚠️ 用户未登录，将使用测试模式');
                }
            } else {
                console.warn('⚠️ 认证管理器未加载，将使用兼容模式');
            }
        });
    </script>

</a>
<!-- Visual Studio Browser Link -->
<script type="text/javascript" src="/_vs/browserLink" async="async" id="__browserLink_initializationData" data-requestId="8c2bca2c43e6464a82f8571dc1e70487" data-requestMappingFromServer="false" data-connectUrl="http://localhost:52634/583a38e14bff42bb96e5f3acc057bea3/browserLink"></script>
<!-- End Browser Link -->
</body></html>