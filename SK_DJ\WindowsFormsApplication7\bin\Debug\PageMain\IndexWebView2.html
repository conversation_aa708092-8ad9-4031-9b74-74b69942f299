﻿<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>WebView2控件 - 平行时空</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0,0,0,0.3);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .button {
            background: #e53e3e;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            margin: 10px;
            transition: background-color 0.3s ease;
        }
        .button:hover { background: #c53030; }
        .return-button {
            background: #38a169;
            font-size: 20px;
            padding: 20px 40px;
        }
        .return-button:hover { background: #2f855a; }
        .info-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .game-area {
            background: rgba(255,255,255,0.05);
            padding: 30px;
            margin: 20px 0;
            border-radius: 15px;
            border: 2px solid rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 平行时空世界</h1>
        <div class='info-box'>
            <h3>欢迎来到平行时空！</h3>
            <p>这里是基于现代WebView2控件的平行世界，拥有更强大的功能和更好的性能。</p>
            <p>在这个时空中，您可以体验到与原始世界不同的游戏内容和功能。</p>
        </div>

        <div class='game-area'>
            <h3>🎮 平行时空游戏区域</h3>
            <p>这里可以放置平行时空特有的游戏功能...</p>
            <button class='button' onclick='testWebView2Features()'>测试现代Web功能</button>
            <button class='button' onclick='callCSharpMethod()'>调用C#方法</button>
            <button class='button' onclick='getSystemInfo()'>获取系统信息</button>
        </div>

        <div class='info-box'>
            <h3>🌟 WebView2 技术特性</h3>
            <p>✅ 基于Microsoft Edge (Chromium)引擎</p>
            <p>✅ 支持最新的Web标准和API</p>
            <p>✅ 更好的性能和安全性</p>
            <p>✅ 现代化的JavaScript支持</p>
            <p>✅ 支持ES6+、WebGL、WebAssembly等现代技术</p>
        </div>

        <button class='button return-button' onclick='returnToOriginalWorld()'>🌀 返回原始时空</button>

        <div id='messageArea' class='info-box' style='margin-top: 20px;'>
            <h3>📨 时空通信日志</h3>
            <div id='messages' style='max-height: 200px; overflow-y: auto; text-align: left; background: rgba(0,0,0,0.2); padding: 10px; border-radius: 5px;'>
                <p style='color: #90EE90;'>✅ 平行时空已成功建立连接...</p>
            </div>
        </div>
    </div>

    <script>
        // 返回原始时空
        function returnToOriginalWorld() {
            try {
                if (window.chrome && window.chrome.webview) {
                    window.chrome.webview.postMessage('switchToWebBrowser');
                    addMessage('正在穿越回原始时空...', 'info');

                    // 添加视觉效果
                    document.body.style.transition = 'opacity 0.5s ease-out';
                    document.body.style.opacity = '0.5';

                    setTimeout(() => {
                        addMessage('时空穿越请求已发送', 'success');
                    }, 500);
                } else {
                    addMessage('时空穿越失败：通信接口不可用', 'error');
                }
            } catch (e) {
                addMessage('时空穿越异常: ' + e.message, 'error');
            }
        }

        // 测试WebView2现代功能
        function testWebView2Features() {
            const features = [];

            // 测试现代Web API
            if ('fetch' in window) features.push('✅ Fetch API');
            else features.push('❌ Fetch API');

            if ('Promise' in window) features.push('✅ Promise');
            else features.push('❌ Promise');

            if ('localStorage' in window) features.push('✅ LocalStorage');
            else features.push('❌ LocalStorage');

            if ('WebSocket' in window) features.push('✅ WebSocket');
            else features.push('❌ WebSocket');

            if ('Worker' in window) features.push('✅ Web Workers');
            else features.push('❌ Web Workers');

            if ('WebGL2RenderingContext' in window) features.push('✅ WebGL 2.0');
            else features.push('❌ WebGL 2.0');

            addMessage('平行时空技术检测结果:<br>' + features.join('<br>'), 'success');
        }

        // 调用C#方法
        function callCSharpMethod() {
            try {
                if (window.chrome && window.chrome.webview) {
                    window.chrome.webview.postMessage('callCSharpMethod');
                    addMessage('已向原始时空发送C#调用请求', 'success');
                } else {
                    addMessage('无法与原始时空通信：接口不可用', 'error');
                }
            } catch (e) {
                addMessage('通信失败: ' + e.message, 'error');
            }
        }

        // 获取系统信息
        function getSystemInfo() {
            try {
                if (window.chrome && window.chrome.webview) {
                    window.chrome.webview.postMessage('getSystemInfo');
                    addMessage('正在从原始时空获取系统信息...', 'info');
                } else {
                    addMessage('无法获取系统信息：通信接口不可用', 'error');
                }
            } catch (e) {
                addMessage('获取系统信息失败: ' + e.message, 'error');
            }
        }

        // 添加消息到日志区域
        function addMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const timestamp = new Date().toLocaleString('zh-CN');

            let color = '#fff';
            let icon = '📝';
            switch(type) {
                case 'success': color = '#90EE90'; icon = '✅'; break;
                case 'error': color = '#FFB6C1'; icon = '❌'; break;
                case 'warning': color = '#FFD700'; icon = '⚠️'; break;
                case 'info': color = '#87CEEB'; icon = '🌀'; break;
                default: color = '#87CEEB'; icon = '📝'; break;
            }

            const messageElement = document.createElement('div');
            messageElement.style.color = color;
            messageElement.style.marginBottom = '5px';
            messageElement.style.fontSize = '14px';
            messageElement.innerHTML = `${icon} [${timestamp}] ${message}`;

            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 页面加载完成
        window.onload = function() {
            addMessage('平行时空世界初始化完成！', 'success');

            if (window.chrome && window.chrome.webview) {
                addMessage('时空通信链路已建立', 'success');
                addMessage('您现在位于平行时空，可以体验现代Web技术', 'info');
            } else {
                addMessage('警告: 时空通信链路异常', 'warning');
            }

            // 添加一些动态效果
            setTimeout(() => {
                addMessage('平行时空环境稳定，所有系统正常运行', 'success');
            }, 2000);
        };
    </script>
</body>
</html>