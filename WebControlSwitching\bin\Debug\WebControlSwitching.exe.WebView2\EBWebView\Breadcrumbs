0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:01 Memory Pressure: Critical
0:02:12 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #link
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 PageLoad
0:00:01 Memory Pressure: Critical
0:00:07 Tab1 StartNav3 #link
0:00:07 Tab1 FinishNav3
0:00:07 Tab1 PageLoad
0:00:11 Tab1 StartNav4 #link
0:00:11 Tab1 FinishNav4
0:00:11 Tab1 PageLoad
0:00:13 Tab1 StartNav5 #link
0:00:13 Tab1 FinishNav5
0:00:13 Tab1 PageLoad
0:00:14 Tab1 StartNav6 #link
0:00:14 Tab1 FinishNav6
0:00:14 Tab1 PageLoad
0:00:16 Tab1 StartNav7 #link
0:00:16 Tab1 FinishNav7
0:00:16 Tab1 PageLoad
0:00:21 Tab1 StartNav8 #link
0:00:21 Tab1 FinishNav8
0:00:21 Tab1 PageLoad
0:00:22 Tab1 StartNav9 #link
0:00:22 Tab1 FinishNav9
0:00:22 Tab1 PageLoad
0:00:24 Tab1 StartNav10 #link
0:00:24 Tab1 FinishNav10
0:00:24 Tab1 PageLoad
0:07:05 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 PageLoad
0:00:01 Memory Pressure: Critical
0:00:03 Tab1 StartNav3 #typed
0:00:03 Tab1 FinishNav3
0:00:03 Tab1 PageLoad
0:00:06 Tab1 StartNav4 #typed
0:00:06 Tab1 FinishNav4
0:00:06 Tab1 PageLoad
0:00:07 Tab1 StartNav5 #typed
0:00:07 Tab1 FinishNav5
0:00:07 Tab1 PageLoad
0:00:09 Tab1 StartNav6 #typed
0:00:09 Tab1 FinishNav6
0:00:09 Tab1 PageLoad
0:00:10 Tab1 StartNav7 #typed
0:00:10 Tab1 FinishNav7
0:00:10 Tab1 PageLoad
0:00:12 Tab1 StartNav8 #typed
0:00:12 Tab1 FinishNav8
0:00:12 Tab1 PageLoad
0:00:14 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 PageLoad
0:00:01 Memory Pressure: Critical
0:00:02 Tab1 StartNav3 #typed
0:00:02 Tab1 FinishNav3
0:00:02 Tab1 PageLoad
0:00:04 Tab1 StartNav4 #typed
0:00:04 Tab1 FinishNav4
0:00:04 Tab1 PageLoad
0:00:07 Tab1 StartNav5 #typed
0:00:07 Tab1 FinishNav5
0:00:07 Tab1 PageLoad
0:00:11 Tab1 StartNav6 #typed
0:00:11 Tab1 FinishNav6
0:00:11 Tab1 PageLoad
0:00:12 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 PageLoad
0:00:01 Memory Pressure: Critical
0:00:03 Tab1 StartNav3 #typed
0:00:03 Tab1 FinishNav3
0:00:03 Tab1 PageLoad
0:00:11 Tab1 StartNav4 #typed
0:00:11 Tab1 FinishNav4
0:00:11 Tab1 PageLoad
0:00:38 Tab1 StartNav5 #typed
0:00:38 Tab1 FinishNav5
0:00:38 Tab1 PageLoad
0:00:54 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
